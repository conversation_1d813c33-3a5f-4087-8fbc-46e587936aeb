#!/usr/bin/env python3
"""
iCalendar Database Synchronization Demo
Demonstrates the complete data extraction and database sync system.
"""

from ical_database_sync import ICalDatabaseSync
import json
from datetime import datetime

def main():
    print("=== iCalendar Database Synchronization System ===")
    print("=" * 55)
    
    # Initialize the sync system
    sync_system = ICalDatabaseSync("ical_data.db")
    
    # Step 1: Load properties from CSV
    print("\n📥 Step 1: Loading properties from CSV...")
    try:
        sync_system.load_properties_from_csv("ical.csv")
        print("✅ Properties loaded successfully!")
    except Exception as e:
        print(f"❌ Failed to load properties: {e}")
        return
    
    # Step 2: Show initial statistics
    print("\n📊 Step 2: Initial Statistics...")
    stats = sync_system.get_property_stats()
    print(f"Total Properties: {stats['total_properties']}")
    print(f"Platform Distribution: {stats['platform_distribution']}")
    
    # Step 3: Sync a few sample properties first
    print("\n🔄 Step 3: Syncing sample properties...")
    
    # Get first 5 properties for demo
    import sqlite3
    conn = sqlite3.connect("ical_data.db")
    cursor = conn.cursor()
    cursor.execute("SELECT property_id, platform FROM properties LIMIT 5")
    sample_properties = cursor.fetchall()
    conn.close()
    
    for property_id, platform in sample_properties:
        print(f"\n🔄 Syncing Property {property_id} ({platform})...")
        result = sync_system.sync_property(property_id)
        
        if result['status'] == 'success':
            print(f"  ✅ Success: {result['events_processed']} events processed")
            print(f"     Added: {result['events_added']}, Updated: {result['events_updated']}, Deleted: {result['events_deleted']}")
        else:
            print(f"  ❌ Failed: {result['error']}")
    
    # Step 4: Show updated statistics
    print("\n📊 Step 4: Updated Statistics...")
    stats = sync_system.get_property_stats()
    print(f"Total Properties: {stats['total_properties']}")
    print(f"Synced Properties: {stats['synced_properties']}")
    print(f"Total Events: {stats['total_events']}")
    
    # Step 5: Show sample property details
    if sample_properties:
        sample_property_id = sample_properties[0][0]
        print(f"\n🏠 Step 5: Sample Property Details ({sample_property_id})...")
        
        property_stats = sync_system.get_property_stats(sample_property_id)
        if 'error' not in property_stats:
            print("Property Summary:")
            if property_stats['property_summary']:
                summary = property_stats['property_summary']
                print(f"  Platform: {summary[1]}")
                print(f"  Last Sync: {summary[2]}")
                print(f"  Total Events: {summary[4]}")
                print(f"  Bookings: {summary[5]}")
                print(f"  Blocked Periods: {summary[6]}")
            
            if property_stats['occupancy_stats']:
                occupancy = property_stats['occupancy_stats']
                print(f"\nOccupancy Stats:")
                print(f"  Total Days: {occupancy[2]}")
                print(f"  Occupied Days: {occupancy[3]}")
                print(f"  Occupancy Rate: {occupancy[4]}%")
                print(f"  Period: {occupancy[5]} to {occupancy[6]}")
    
    # Step 6: Sample queries
    print("\n🔍 Step 6: Sample Database Queries...")
    
    conn = sqlite3.connect("ical_data.db")
    cursor = conn.cursor()
    
    # Recent bookings
    print("\nRecent Bookings:")
    cursor.execute("""
        SELECT property_id, platform, start_date, end_date, summary
        FROM calendar_events 
        WHERE event_type = 'booking' 
        ORDER BY start_date DESC 
        LIMIT 5
    """)
    
    for row in cursor.fetchall():
        print(f"  Property {row[0]} ({row[1]}): {row[2]} to {row[3]} - {row[4]}")
    
    # Platform summary
    print("\nPlatform Event Summary:")
    cursor.execute("""
        SELECT platform, event_type, COUNT(*) as count
        FROM calendar_events 
        GROUP BY platform, event_type
        ORDER BY platform, event_type
    """)
    
    for row in cursor.fetchall():
        print(f"  {row[0]} - {row[1]}: {row[2]} events")
    
    conn.close()
    
    print("\n🎉 Demo completed! Database is ready for advanced analytics.")
    print("\n💡 Next Steps:")
    print("   - Run sync_system.sync_all_properties() to sync all properties")
    print("   - Use the database for occupancy analysis, revenue optimization, etc.")
    print("   - Build dashboards and reporting tools on top of this data")

if __name__ == "__main__":
    main()
