#!/usr/bin/env python3
"""
Fix Occupancy Logic Migration Script

This script updates the iCalendar Analytics System to properly distinguish between
actual bookings and blocked/unavailable time, providing accurate occupancy rates.

Key Changes:
1. Re-classify existing events using improved logic
2. Regenerate daily availability data with correct event types
3. Update database views with corrected occupancy calculations
4. Provide before/after comparison of occupancy rates
"""

import sqlite3
import sys
from datetime import datetime
from ical_database_sync import ICalDatabaseSync

def backup_database():
    """Create a backup of the current database."""
    import shutil
    backup_name = f"ical_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    shutil.copy2('ical_data.db', backup_name)
    print(f"✅ Database backed up to: {backup_name}")
    return backup_name

def analyze_current_occupancy():
    """Analyze current occupancy rates before the fix."""
    conn = sqlite3.connect('ical_data.db')
    
    print("📊 CURRENT OCCUPANCY ANALYSIS (Before Fix)")
    print("=" * 50)
    
    # Overall stats
    cursor = conn.execute("""
        SELECT 
            COUNT(*) as total_days,
            SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) as occupied_days,
            ROUND((SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) * 100.0) / COUNT(*), 2) as occupancy_rate
        FROM daily_availability
    """)
    overall = cursor.fetchone()
    print(f"Overall: {overall[1]}/{overall[0]} days occupied ({overall[2]}%)")
    
    # Top properties by occupancy
    cursor = conn.execute("""
        SELECT property_id, platform,
               COUNT(*) as total_days,
               SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) as occupied_days,
               ROUND((SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) * 100.0) / COUNT(*), 2) as occupancy_rate
        FROM daily_availability
        GROUP BY property_id, platform
        HAVING COUNT(*) > 100
        ORDER BY occupancy_rate DESC
        LIMIT 10
    """)
    
    print(f"\nTop 10 Properties by Current Occupancy Rate:")
    for row in cursor.fetchall():
        print(f"  Property {row[0]} ({row[1]}): {row[4]}% ({row[3]}/{row[2]} days)")
    
    # Event type analysis
    cursor = conn.execute("""
        SELECT event_type, COUNT(*) as count
        FROM daily_availability 
        WHERE NOT is_available
        GROUP BY event_type
        ORDER BY count DESC
    """)
    
    print(f"\nCurrent Event Type Distribution (Unavailable Days):")
    for row in cursor.fetchall():
        event_type = row[0] if row[0] else 'NULL'
        print(f"  {event_type}: {row[1]} days")
    
    conn.close()
    return overall

def reclassify_events():
    """Re-classify existing calendar events using improved logic."""
    print("\n🔄 RE-CLASSIFYING CALENDAR EVENTS")
    print("=" * 40)
    
    conn = sqlite3.connect('ical_data.db')
    sync_engine = ICalDatabaseSync('ical_data.db')
    
    # Get all calendar events
    cursor = conn.execute("SELECT id, summary, description, event_type FROM calendar_events")
    events = cursor.fetchall()
    
    reclassified_count = 0
    booking_count = 0
    blocked_count = 0
    
    for event_id, summary, description, old_type in events:
        # Use the improved classification logic
        new_type = sync_engine.determine_event_type(summary or '', description or '')
        
        if new_type != old_type:
            conn.execute(
                "UPDATE calendar_events SET event_type = ? WHERE id = ?",
                (new_type, event_id)
            )
            reclassified_count += 1
        
        if new_type == 'booking':
            booking_count += 1
        elif new_type == 'blocked':
            blocked_count += 1
    
    conn.commit()
    
    print(f"✅ Re-classified {reclassified_count} events")
    print(f"   📅 Booking events: {booking_count}")
    print(f"   🚫 Blocked events: {blocked_count}")
    print(f"   📊 Total events processed: {len(events)}")
    
    conn.close()

def regenerate_daily_availability():
    """Regenerate daily availability data with corrected event types."""
    print("\n🔄 REGENERATING DAILY AVAILABILITY DATA")
    print("=" * 45)
    
    conn = sqlite3.connect('ical_data.db')
    sync_engine = ICalDatabaseSync('ical_data.db')
    
    # Get all properties
    cursor = conn.execute("SELECT property_id, platform FROM properties")
    properties = cursor.fetchall()
    
    # Clear existing daily availability data
    conn.execute("DELETE FROM daily_availability")
    print(f"🗑️  Cleared existing daily availability data")
    
    # Regenerate for each property
    for property_id, platform in properties:
        try:
            sync_engine.generate_daily_availability(property_id, platform)
            print(f"✅ Regenerated availability for property {property_id}")
        except Exception as e:
            print(f"❌ Error regenerating property {property_id}: {e}")
    
    conn.commit()
    conn.close()
    
    print(f"✅ Daily availability data regenerated for {len(properties)} properties")

def update_database_views():
    """Update database views with corrected occupancy logic."""
    print("\n🔄 UPDATING DATABASE VIEWS")
    print("=" * 30)
    
    conn = sqlite3.connect('ical_data.db')
    
    # Drop existing view
    conn.execute("DROP VIEW IF EXISTS occupancy_stats")
    
    # Create updated view with corrected logic
    conn.execute("""
        CREATE VIEW occupancy_stats AS
        SELECT 
            property_id,
            platform,
            COUNT(*) as total_days,
            SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) as available_days,
            SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) as booked_days,
            SUM(CASE WHEN event_type = 'blocked' THEN 1 ELSE 0 END) as blocked_days,
            SUM(CASE WHEN NOT is_available AND event_type != 'booking' THEN 1 ELSE 0 END) as other_unavailable_days,
            ROUND(
                CASE 
                    WHEN (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END)) > 0
                    THEN (SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) * 100.0) / 
                         (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END))
                    ELSE 0
                END,
                2
            ) as true_occupancy_rate,
            ROUND(
                (SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) * 100.0) / COUNT(*), 
                2
            ) as calendar_blocked_rate,
            MIN(date) as period_start,
            MAX(date) as period_end
        FROM daily_availability
        GROUP BY property_id, platform
    """)
    
    conn.commit()
    conn.close()
    
    print("✅ Database views updated with corrected occupancy logic")

def analyze_fixed_occupancy():
    """Analyze occupancy rates after the fix."""
    conn = sqlite3.connect('ical_data.db')
    
    print("\n📊 FIXED OCCUPANCY ANALYSIS (After Fix)")
    print("=" * 45)
    
    # Overall stats with new metrics
    cursor = conn.execute("""
        SELECT 
            COUNT(*) as total_days,
            SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) as available_days,
            SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) as booked_days,
            SUM(CASE WHEN event_type = 'blocked' THEN 1 ELSE 0 END) as blocked_days,
            ROUND(
                CASE 
                    WHEN (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END)) > 0
                    THEN (SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) * 100.0) / 
                         (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END))
                    ELSE 0
                END,
                2
            ) as true_occupancy_rate
        FROM daily_availability
    """)
    overall = cursor.fetchone()
    
    print(f"Overall Metrics:")
    print(f"  Total Days: {overall[0]}")
    print(f"  Available Days: {overall[1]}")
    print(f"  Booked Days: {overall[2]}")
    print(f"  Blocked Days: {overall[3]}")
    print(f"  TRUE Occupancy Rate: {overall[4]}%")
    
    # Top properties by TRUE occupancy
    cursor = conn.execute("""
        SELECT property_id, platform, total_days, available_days, booked_days, blocked_days, true_occupancy_rate
        FROM occupancy_stats
        WHERE total_days > 100
        ORDER BY true_occupancy_rate DESC
        LIMIT 10
    """)
    
    print(f"\nTop 10 Properties by TRUE Occupancy Rate:")
    for row in cursor.fetchall():
        print(f"  Property {row[0]} ({row[1]}): {row[6]}% ({row[4]} booked / {row[3]} available)")
    
    conn.close()
    return overall

def main():
    """Main migration function."""
    print("🔧 OCCUPANCY LOGIC FIX MIGRATION")
    print("=" * 40)
    print("This will fix the occupancy calculation logic to distinguish")
    print("between actual bookings and blocked/unavailable time.")
    print()
    
    # Confirm migration
    response = input("❓ Continue with migration? (yes/no): ").lower().strip()
    if response != 'yes':
        print("❌ Migration cancelled.")
        return
    
    try:
        # Step 1: Backup database
        backup_file = backup_database()
        
        # Step 2: Analyze current state
        old_stats = analyze_current_occupancy()
        
        # Step 3: Re-classify events
        reclassify_events()
        
        # Step 4: Regenerate daily availability
        regenerate_daily_availability()
        
        # Step 5: Update database views
        update_database_views()
        
        # Step 6: Analyze fixed state
        new_stats = analyze_fixed_occupancy()
        
        # Step 7: Summary
        print("\n🎯 MIGRATION SUMMARY")
        print("=" * 25)
        print(f"✅ Migration completed successfully!")
        print(f"📁 Backup saved: {backup_file}")
        print(f"📊 Old occupancy rate: {old_stats[2]}% (inflated)")
        print(f"📊 New TRUE occupancy rate: {new_stats[4]}% (accurate)")
        print(f"📈 Improvement: Now showing realistic booking-based occupancy")
        
        print(f"\n🎉 The system now provides accurate occupancy data!")
        print(f"   - Blocked time is no longer counted as occupied")
        print(f"   - Only actual guest bookings count toward occupancy")
        print(f"   - Analytics show meaningful business metrics")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        print(f"💡 Database backup available: {backup_file if 'backup_file' in locals() else 'Not created'}")
        sys.exit(1)

if __name__ == "__main__":
    main()
