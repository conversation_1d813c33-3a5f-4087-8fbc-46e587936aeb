#!/usr/bin/env python3
"""
<PERSON>ript to delete all Darent properties and their associated data from the iCal checker database.
This removes properties with misleading "Not Available" occupancy data.
"""

import sqlite3
import sys
from datetime import datetime

def delete_darent_properties():
    """Delete all Darent properties and their associated data."""

    # Connect to database
    conn = sqlite3.connect('ical_data.db')
    conn.row_factory = sqlite3.Row

    try:
        # Start transaction
        conn.execute('BEGIN TRANSACTION')

        print("🔍 Analyzing Darent properties to delete...")

        # Get list of Darent property IDs
        darent_properties = conn.execute("""
            SELECT property_id, ical_url
            FROM properties
            WHERE ical_url LIKE '%darent.com%'
        """).fetchall()

        if not darent_properties:
            print("✅ No Darent properties found in database.")
            return

        property_ids = [prop['property_id'] for prop in darent_properties]
        property_ids_str = "', '".join(property_ids)

        print(f"📊 Found {len(darent_properties)} Darent properties to delete:")
        for prop in darent_properties[:5]:  # Show first 5
            print(f"   - Property {prop['property_id']}: {prop['ical_url']}")
        if len(darent_properties) > 5:
            print(f"   ... and {len(darent_properties) - 5} more")

        # Count associated data before deletion
        print("\n📈 Counting associated data...")

        calendar_events_count = conn.execute(f"""
            SELECT COUNT(*) as count
            FROM calendar_events
            WHERE property_id IN ('{property_ids_str}')
        """).fetchone()['count']

        daily_availability_count = conn.execute(f"""
            SELECT COUNT(*) as count
            FROM daily_availability
            WHERE property_id IN ('{property_ids_str}')
        """).fetchone()['count']

        sync_history_count = conn.execute(f"""
            SELECT COUNT(*) as count
            FROM sync_history
            WHERE property_id IN ('{property_ids_str}')
        """).fetchone()['count']

        print(f"   - Calendar Events: {calendar_events_count}")
        print(f"   - Daily Availability: {daily_availability_count}")
        print(f"   - Sync History: {sync_history_count}")

        # Confirm deletion
        print(f"\n⚠️  This will permanently delete:")
        print(f"   - {len(darent_properties)} Darent properties")
        print(f"   - {calendar_events_count} calendar events")
        print(f"   - {daily_availability_count} daily availability records")
        print(f"   - {sync_history_count} sync history records")

        response = input("\n❓ Continue with deletion? (yes/no): ").lower().strip()

        if response != 'yes':
            print("❌ Deletion cancelled.")
            conn.rollback()
            return

        print("\n🗑️  Starting deletion process...")

        # Delete in correct order (foreign key constraints)

        # 1. Delete calendar events
        print("   Deleting calendar events...")
        conn.execute(f"""
            DELETE FROM calendar_events
            WHERE property_id IN ('{property_ids_str}')
        """)

        # 2. Delete daily availability records
        print("   Deleting daily availability records...")
        conn.execute(f"""
            DELETE FROM daily_availability
            WHERE property_id IN ('{property_ids_str}')
        """)

        # 3. Delete sync history
        print("   Deleting sync history...")
        conn.execute(f"""
            DELETE FROM sync_history
            WHERE property_id IN ('{property_ids_str}')
        """)

        # 4. Delete properties
        print("   Deleting properties...")
        conn.execute("""
            DELETE FROM properties
            WHERE ical_url LIKE '%darent.com%'
        """)

        # Commit transaction
        conn.commit()

        print("\n✅ Successfully deleted all Darent properties and associated data!")

        # Show final counts
        print("\n📊 Final database statistics:")
        remaining_properties = conn.execute("SELECT COUNT(*) as count FROM properties").fetchone()['count']
        remaining_events = conn.execute("SELECT COUNT(*) as count FROM calendar_events").fetchone()['count']

        print(f"   - Remaining properties: {remaining_properties}")
        print(f"   - Remaining calendar events: {remaining_events}")

        # Show platform distribution
        print("\n🏢 Remaining platforms:")
        platforms = conn.execute("""
            SELECT
                CASE
                    WHEN ical_url LIKE '%airbnb.com%' THEN 'Airbnb'
                    WHEN ical_url LIKE '%booking.com%' THEN 'Booking.com'
                    WHEN ical_url LIKE '%vrbo.com%' THEN 'VRBO'
                    ELSE 'Other'
                END as platform,
                COUNT(*) as count
            FROM properties
            GROUP BY platform
            ORDER BY count DESC
        """).fetchall()

        for platform in platforms:
            print(f"   - {platform['platform']}: {platform['count']} properties")

        print(f"\n🎯 Deletion completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    except Exception as e:
        print(f"\n❌ Error during deletion: {e}")
        conn.rollback()
        raise

    finally:
        conn.close()

if __name__ == "__main__":
    print("🧹 Darent Properties Cleanup Script")
    print("=" * 50)
    delete_darent_properties()
