-- iCalendar Data Extraction Database Schema
-- SQLite database for storing and analyzing property availability data

-- Properties table - stores basic property information
CREATE TABLE IF NOT EXISTS properties (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    property_id TEXT UNIQUE NOT NULL,
    ical_url TEXT NOT NULL,
    platform TEXT NOT NULL, -- airbnb, booking, lodgify, darent, etc.
    last_sync TIMESTAMP,
    sync_status TEXT DEFAULT 'pending', -- pending, success, failed
    sync_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Calendar events table - stores individual booking/availability events
CREATE TABLE IF NOT EXISTS calendar_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    property_id TEXT NOT NULL,
    event_uid TEXT NOT NULL, -- Unique identifier from iCal
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    summary TEXT, -- Reserved, Not Available, etc.
    description TEXT,
    event_type TEXT, -- booking, blocked, unavailable
    platform TEXT NOT NULL,
    reservation_url TEXT,
    guest_phone_last4 TEXT,
    dtstamp TIMESTAMP, -- When event was created/modified
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties (property_id),
    UNIQUE(property_id, event_uid, platform)
);

-- Sync history table - tracks synchronization attempts
CREATE TABLE IF NOT EXISTS sync_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    property_id TEXT NOT NULL,
    sync_start TIMESTAMP NOT NULL,
    sync_end TIMESTAMP,
    status TEXT NOT NULL, -- success, failed, partial
    events_processed INTEGER DEFAULT 0,
    events_added INTEGER DEFAULT 0,
    events_updated INTEGER DEFAULT 0,
    events_deleted INTEGER DEFAULT 0,
    error_message TEXT,
    FOREIGN KEY (property_id) REFERENCES properties (property_id)
);

-- Daily availability summary table - for faster analytics
CREATE TABLE IF NOT EXISTS daily_availability (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    property_id TEXT NOT NULL,
    date DATE NOT NULL,
    is_available BOOLEAN NOT NULL,
    event_type TEXT, -- booking, blocked, unavailable
    platform TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties (property_id),
    UNIQUE(property_id, date, platform)
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_properties_platform ON properties(platform);
CREATE INDEX IF NOT EXISTS idx_properties_last_sync ON properties(last_sync);
CREATE INDEX IF NOT EXISTS idx_calendar_events_property_dates ON calendar_events(property_id, start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_calendar_events_platform ON calendar_events(platform);
CREATE INDEX IF NOT EXISTS idx_calendar_events_type ON calendar_events(event_type);
CREATE INDEX IF NOT EXISTS idx_daily_availability_property_date ON daily_availability(property_id, date);
CREATE INDEX IF NOT EXISTS idx_daily_availability_date ON daily_availability(date);
CREATE INDEX IF NOT EXISTS idx_sync_history_property ON sync_history(property_id);

-- Views for common analytics queries
CREATE VIEW IF NOT EXISTS property_summary AS
SELECT
    p.property_id,
    p.platform,
    p.last_sync,
    p.sync_status,
    COUNT(DISTINCT ce.id) as total_events,
    COUNT(DISTINCT CASE WHEN ce.event_type = 'booking' THEN ce.id END) as bookings,
    COUNT(DISTINCT CASE WHEN ce.event_type = 'blocked' THEN ce.id END) as blocked_periods,
    MIN(ce.start_date) as earliest_event,
    MAX(ce.end_date) as latest_event
FROM properties p
LEFT JOIN calendar_events ce ON p.property_id = ce.property_id
GROUP BY p.property_id, p.platform, p.last_sync, p.sync_status;

CREATE VIEW IF NOT EXISTS occupancy_stats AS
SELECT
    property_id,
    platform,
    COUNT(*) as total_days,
    SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) as available_days,
    SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) as booked_days,
    SUM(CASE WHEN event_type = 'blocked' THEN 1 ELSE 0 END) as blocked_days,
    SUM(CASE WHEN NOT is_available AND event_type != 'booking' THEN 1 ELSE 0 END) as other_unavailable_days,
    ROUND(
        CASE
            WHEN (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END)) > 0
            THEN (SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) * 100.0) /
                 (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END))
            ELSE 0
        END,
        2
    ) as true_occupancy_rate,
    ROUND(
        (SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) * 100.0) / COUNT(*),
        2
    ) as calendar_blocked_rate,
    MIN(date) as period_start,
    MAX(date) as period_end
FROM daily_availability
GROUP BY property_id, platform;
