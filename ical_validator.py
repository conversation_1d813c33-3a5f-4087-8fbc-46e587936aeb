import csv
import requests
from datetime import datetime
from urllib3.exceptions import InsecureRequestWarning
from requests.exceptions import RequestException
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import sys
import time
import random
from urllib.parse import urlparse
from collections import defaultdict, deque

# Suppress only the specific InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class DomainRateLimiter:
    """Rate limiter that manages request timing per domain to avoid 429 errors."""

    def __init__(self):
        self.domain_locks = defaultdict(Lock)
        self.domain_last_request = defaultdict(float)
        self.domain_delays = {
            'airbnb.com': 0.5,      # 0.5 seconds between Airbnb requests
            'ar.airbnb.com': 0.5,   # Same for Arabic Airbnb
            'booking.com': 0.3,     # 0.3 seconds between Booking.com requests
            'lodgify.com': 0.2,     # 0.2 seconds between Lodgify requests
            'darent.com': 0.1,      # 0.1 seconds between Darent requests
            'gathern.co': 0.2,      # 0.2 seconds between Gathern requests
            'guesty.com': 0.3,      # 0.3 seconds between Guesty requests
            'agoda.com': 0.3,       # 0.3 seconds between Agoda requests
        }
        self.default_delay = 0.1    # Default delay for unknown domains

    def get_domain(self, url):
        """Extract domain from URL."""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            # Remove 'www.' prefix if present
            if domain.startswith('www.'):
                domain = domain[4:]
            return domain
        except:
            return 'unknown'

    def wait_for_domain(self, url):
        """Wait appropriate time before making request to avoid rate limiting."""
        domain = self.get_domain(url)
        delay = self.domain_delays.get(domain, self.default_delay)

        with self.domain_locks[domain]:
            now = time.time()
            last_request = self.domain_last_request[domain]
            time_since_last = now - last_request

            if time_since_last < delay:
                sleep_time = delay - time_since_last
                time.sleep(sleep_time)

            self.domain_last_request[domain] = time.time()

# Global rate limiter instance
rate_limiter = DomainRateLimiter()

def validate_single_ical_url(row_data, timeout=10, max_retries=3):
    """
    Validates a single iCalendar URL with rate limiting and retry logic.

    Args:
        row_data (dict): Dictionary containing 'ID', 'Property ID', and 'Icalendar URL'
        timeout (int): Timeout in seconds for URL requests
        max_retries (int): Maximum number of retry attempts for 429 errors

    Returns:
        dict: Result dictionary with success status and error details if failed
    """
    id_value = row_data['ID']
    property_id = row_data['Property ID']
    url = row_data['Icalendar URL'].strip()

    # Headers to appear more legitimate
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/calendar,text/plain,*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    last_error = None

    for attempt in range(max_retries + 1):
        try:
            # Apply rate limiting before making request
            rate_limiter.wait_for_domain(url)

            # Add small random delay to spread requests
            if attempt > 0:
                jitter = random.uniform(0.1, 0.5)
                time.sleep(jitter)

            # Attempt to download the iCalendar file
            response = requests.get(url, timeout=timeout, verify=False, headers=headers)

            # Handle rate limiting with exponential backoff
            if response.status_code == 429:
                if attempt < max_retries:
                    # Exponential backoff: 2^attempt seconds + jitter
                    backoff_time = (2 ** attempt) + random.uniform(0.5, 1.5)
                    time.sleep(backoff_time)
                    last_error = f"HTTP Error: 429 (Rate Limited) - Attempt {attempt + 1}/{max_retries + 1}"
                    continue
                else:
                    return {
                        'success': False,
                        'ID': id_value,
                        'Property ID': property_id,
                        'URL': url,
                        'Error': f"HTTP Error: 429 (Rate Limited) - Failed after {max_retries + 1} attempts"
                    }

            # Check if the request was successful
            if response.status_code != 200:
                return {
                    'success': False,
                    'ID': id_value,
                    'Property ID': property_id,
                    'URL': url,
                    'Error': f"HTTP Error: {response.status_code}"
                }

            # Check if content starts with BEGIN:VCALENDAR (basic iCal validation)
            content = response.text.strip()
            if not content.startswith('BEGIN:VCALENDAR'):
                return {
                    'success': False,
                    'ID': id_value,
                    'Property ID': property_id,
                    'URL': url,
                    'Error': "Invalid iCalendar format"
                }

            return {
                'success': True,
                'ID': id_value,
                'Property ID': property_id,
                'URL': url
            }

        except RequestException as e:
            # Handle request exceptions (timeout, connection error, etc.)
            last_error = f"Request Error: {str(e)}"
            if attempt < max_retries:
                # Brief retry delay for network errors
                time.sleep(1 + random.uniform(0.1, 0.5))
                continue

    # If we get here, all retries failed
    return {
        'success': False,
        'ID': id_value,
        'Property ID': property_id,
        'URL': url,
        'Error': last_error or "Unknown error after retries"
    }

def validate_ical_urls(csv_file, timeout=10, max_workers=8, max_retries=3):
    """
    Validates iCalendar URLs from a CSV file in parallel with rate limiting and retry logic.

    Args:
        csv_file (str): Path to the CSV file containing iCalendar URLs
        timeout (int): Timeout in seconds for URL requests
        max_workers (int): Maximum number of parallel workers (threads) - reduced for rate limiting
        max_retries (int): Maximum number of retry attempts for 429 errors
    """
    # Initialize counters and result lists
    total_urls = 0
    working_urls = 0
    failed_urls = []

    # Read all rows from CSV file first
    print(f"Reading CSV file: {csv_file}")
    rows = []
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        rows = list(reader)

    total_urls = len(rows)
    print(f"Found {total_urls} URLs to validate")
    print(f"Using {max_workers} parallel workers with rate limiting")
    print(f"Max retries per URL: {max_retries}")
    print("Starting parallel validation with intelligent rate limiting...")

    # Use ThreadPoolExecutor for parallel processing
    completed_count = 0
    progress_lock = Lock()

    def print_progress():
        with progress_lock:
            nonlocal completed_count
            completed_count += 1
            progress = (completed_count / total_urls) * 100
            print(f"Progress: {completed_count}/{total_urls} ({progress:.1f}%)", end='\r')
            sys.stdout.flush()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all validation tasks with retry parameter
        future_to_row = {
            executor.submit(validate_single_ical_url, row, timeout, max_retries): row
            for row in rows
        }

        # Process completed tasks
        for future in as_completed(future_to_row):
            result = future.result()
            print_progress()

            if result['success']:
                working_urls += 1
            else:
                failed_urls.append({
                    'ID': result['ID'],
                    'Property ID': result['Property ID'],
                    'URL': result['URL'],
                    'Error': result['Error']
                })

    print()  # New line after progress
    print("Parallel validation completed!")

    # Generate the report
    generate_report(total_urls, working_urls, failed_urls)

def generate_report(total_urls, working_urls, failed_urls):
    """
    Generates a report of the URL validation results.

    Args:
        total_urls (int): Total number of URLs checked
        working_urls (int): Number of working URLs
        failed_urls (list): List of dictionaries containing failed URL details
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"ical_validation_report_{timestamp}.txt"

    with open(report_file, 'w', encoding='utf-8') as file:
        file.write("iCalendar URL Validation Report\n")
        file.write("==============================\n\n")
        file.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        file.write(f"Total URLs checked: {total_urls}\n")
        file.write(f"Working URLs: {working_urls} ({working_urls/total_urls*100:.1f}%)\n")
        file.write(f"Failed URLs: {len(failed_urls)} ({len(failed_urls)/total_urls*100:.1f}%)\n\n")

        if failed_urls:
            file.write("Failed URLs Details:\n")
            file.write("--------------------\n\n")

            for i, entry in enumerate(failed_urls, 1):
                file.write(f"{i}. ID: {entry['ID']}, Property ID: {entry['Property ID']}\n")
                file.write(f"   URL: {entry['URL']}\n")
                file.write(f"   Error: {entry['Error']}\n\n")

        file.write("\nEnd of Report")

    print(f"\nValidation complete!")
    print(f"Total URLs: {total_urls}")
    print(f"Working URLs: {working_urls}")
    print(f"Failed URLs: {len(failed_urls)}")
    print(f"Report saved to: {report_file}")

    # Also save failed URLs to CSV for easier processing
    if failed_urls:
        csv_report = f"failed_ical_urls_{timestamp}.csv"
        with open(csv_report, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=['ID', 'Property ID', 'URL', 'Error'])
            writer.writeheader()
            writer.writerows(failed_urls)
        print(f"Failed URLs CSV saved to: {csv_report}")

if __name__ == "__main__":
    # Configuration options - optimized for rate limiting
    CSV_FILE = "ical.csv"
    TIMEOUT = 15  # seconds (increased for retry scenarios)
    MAX_WORKERS = 8  # reduced workers to avoid overwhelming servers
    MAX_RETRIES = 3  # retry attempts for 429 errors

    print("=== Enhanced iCalendar URL Validator with Rate Limiting ===")
    print(f"CSV File: {CSV_FILE}")
    print(f"Timeout: {TIMEOUT} seconds")
    print(f"Max Workers: {MAX_WORKERS} (reduced for rate limiting)")
    print(f"Max Retries: {MAX_RETRIES}")
    print("Features: Domain-based rate limiting, exponential backoff, retry logic")
    print("=" * 65)

    start_time = datetime.now()
    validate_ical_urls(CSV_FILE, timeout=TIMEOUT, max_workers=MAX_WORKERS, max_retries=MAX_RETRIES)
    end_time = datetime.now()

    duration = end_time - start_time
    print(f"\nTotal execution time: {duration.total_seconds():.2f} seconds")
    print("Note: Execution time may be longer due to rate limiting and retries, but with better success rates!")