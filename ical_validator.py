import csv
import requests
from datetime import datetime
from urllib3.exceptions import InsecureRequestWarning
from requests.exceptions import RequestException
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import sys

# Suppress only the specific InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def validate_single_ical_url(row_data, timeout=10):
    """
    Validates a single iCalendar URL.

    Args:
        row_data (dict): Dictionary containing 'ID', 'Property ID', and 'Icalendar URL'
        timeout (int): Timeout in seconds for URL requests

    Returns:
        dict: Result dictionary with success status and error details if failed
    """
    id_value = row_data['ID']
    property_id = row_data['Property ID']
    url = row_data['Icalendar URL'].strip()

    try:
        # Attempt to download the iCalendar file
        response = requests.get(url, timeout=timeout, verify=False)

        # Check if the request was successful
        if response.status_code != 200:
            return {
                'success': False,
                'ID': id_value,
                'Property ID': property_id,
                'URL': url,
                'Error': f"HTTP Error: {response.status_code}"
            }

        # Check if content starts with BEGIN:VCALENDAR (basic iCal validation)
        content = response.text.strip()
        if not content.startswith('BEGIN:VCALENDAR'):
            return {
                'success': False,
                'ID': id_value,
                'Property ID': property_id,
                'URL': url,
                'Error': "Invalid iCalendar format"
            }

        return {
            'success': True,
            'ID': id_value,
            'Property ID': property_id,
            'URL': url
        }

    except RequestException as e:
        # Handle request exceptions (timeout, connection error, etc.)
        return {
            'success': False,
            'ID': id_value,
            'Property ID': property_id,
            'URL': url,
            'Error': f"Request Error: {str(e)}"
        }

def validate_ical_urls(csv_file, timeout=10, max_workers=10):
    """
    Validates iCalendar URLs from a CSV file in parallel and generates a report of failures.

    Args:
        csv_file (str): Path to the CSV file containing iCalendar URLs
        timeout (int): Timeout in seconds for URL requests
        max_workers (int): Maximum number of parallel workers (threads)
    """
    # Initialize counters and result lists
    total_urls = 0
    working_urls = 0
    failed_urls = []

    # Read all rows from CSV file first
    print(f"Reading CSV file: {csv_file}")
    rows = []
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        rows = list(reader)

    total_urls = len(rows)
    print(f"Found {total_urls} URLs to validate")
    print(f"Using {max_workers} parallel workers")
    print("Starting parallel validation...")

    # Use ThreadPoolExecutor for parallel processing
    completed_count = 0
    progress_lock = Lock()

    def print_progress():
        with progress_lock:
            nonlocal completed_count
            completed_count += 1
            progress = (completed_count / total_urls) * 100
            print(f"Progress: {completed_count}/{total_urls} ({progress:.1f}%)", end='\r')
            sys.stdout.flush()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all validation tasks
        future_to_row = {
            executor.submit(validate_single_ical_url, row, timeout): row
            for row in rows
        }

        # Process completed tasks
        for future in as_completed(future_to_row):
            result = future.result()
            print_progress()

            if result['success']:
                working_urls += 1
            else:
                failed_urls.append({
                    'ID': result['ID'],
                    'Property ID': result['Property ID'],
                    'URL': result['URL'],
                    'Error': result['Error']
                })

    print()  # New line after progress
    print("Parallel validation completed!")

    # Generate the report
    generate_report(total_urls, working_urls, failed_urls)

def generate_report(total_urls, working_urls, failed_urls):
    """
    Generates a report of the URL validation results.

    Args:
        total_urls (int): Total number of URLs checked
        working_urls (int): Number of working URLs
        failed_urls (list): List of dictionaries containing failed URL details
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"ical_validation_report_{timestamp}.txt"

    with open(report_file, 'w', encoding='utf-8') as file:
        file.write("iCalendar URL Validation Report\n")
        file.write("==============================\n\n")
        file.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        file.write(f"Total URLs checked: {total_urls}\n")
        file.write(f"Working URLs: {working_urls} ({working_urls/total_urls*100:.1f}%)\n")
        file.write(f"Failed URLs: {len(failed_urls)} ({len(failed_urls)/total_urls*100:.1f}%)\n\n")

        if failed_urls:
            file.write("Failed URLs Details:\n")
            file.write("--------------------\n\n")

            for i, entry in enumerate(failed_urls, 1):
                file.write(f"{i}. ID: {entry['ID']}, Property ID: {entry['Property ID']}\n")
                file.write(f"   URL: {entry['URL']}\n")
                file.write(f"   Error: {entry['Error']}\n\n")

        file.write("\nEnd of Report")

    print(f"\nValidation complete!")
    print(f"Total URLs: {total_urls}")
    print(f"Working URLs: {working_urls}")
    print(f"Failed URLs: {len(failed_urls)}")
    print(f"Report saved to: {report_file}")

    # Also save failed URLs to CSV for easier processing
    if failed_urls:
        csv_report = f"failed_ical_urls_{timestamp}.csv"
        with open(csv_report, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=['ID', 'Property ID', 'URL', 'Error'])
            writer.writeheader()
            writer.writerows(failed_urls)
        print(f"Failed URLs CSV saved to: {csv_report}")

if __name__ == "__main__":
    # Configuration options
    CSV_FILE = "ical.csv"
    TIMEOUT = 10  # seconds
    MAX_WORKERS = 20  # parallel workers (adjust based on your system and network)

    print("=== Parallel iCalendar URL Validator ===")
    print(f"CSV File: {CSV_FILE}")
    print(f"Timeout: {TIMEOUT} seconds")
    print(f"Max Workers: {MAX_WORKERS}")
    print("=" * 40)

    start_time = datetime.now()
    validate_ical_urls(CSV_FILE, timeout=TIMEOUT, max_workers=MAX_WORKERS)
    end_time = datetime.now()

    duration = end_time - start_time
    print(f"\nTotal execution time: {duration.total_seconds():.2f} seconds")