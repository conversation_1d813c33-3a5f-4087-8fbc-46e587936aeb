import csv
import requests
import time
from datetime import datetime
from urllib3.exceptions import InsecureRequestWarning
from requests.exceptions import RequestException

# Suppress only the specific InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def validate_ical_urls(csv_file, timeout=10):
    """
    Validates iCalendar URLs from a CSV file and generates a report of failures.
    
    Args:
        csv_file (str): Path to the CSV file containing iCalendar URLs
        timeout (int): Timeout in seconds for URL requests
    """
    # Initialize counters and result lists
    total_urls = 0
    working_urls = 0
    failed_urls = []
    
    # Read the CSV file
    print(f"Reading CSV file: {csv_file}")
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        # Process each row
        for row in reader:
            total_urls += 1
            id_value = row['ID']
            property_id = row['Property ID']
            url = row['Icalendar URL'].strip()
            
            print(f"Checking URL {total_urls}: {url}")
            
            try:
                # Attempt to download the iCalendar file
                response = requests.get(url, timeout=timeout, verify=False)
                
                # Check if the request was successful
                if response.status_code != 200:
                    failed_urls.append({
                        'ID': id_value,
                        'Property ID': property_id,
                        'URL': url,
                        'Error': f"HTTP Error: {response.status_code}"
                    })
                    continue
                
                # Check if content starts with BEGIN:VCALENDAR (basic iCal validation)
                content = response.text.strip()
                if not content.startswith('BEGIN:VCALENDAR'):
                    failed_urls.append({
                        'ID': id_value,
                        'Property ID': property_id,
                        'URL': url,
                        'Error': "Invalid iCalendar format"
                    })
                    continue
                
                working_urls += 1
                
            except RequestException as e:
                # Handle request exceptions (timeout, connection error, etc.)
                failed_urls.append({
                    'ID': id_value,
                    'Property ID': property_id,
                    'URL': url,
                    'Error': f"Request Error: {str(e)}"
                })
            
            # Add a small delay to avoid overwhelming servers
            time.sleep(0.5)
    
    # Generate the report
    generate_report(total_urls, working_urls, failed_urls)

def generate_report(total_urls, working_urls, failed_urls):
    """
    Generates a report of the URL validation results.
    
    Args:
        total_urls (int): Total number of URLs checked
        working_urls (int): Number of working URLs
        failed_urls (list): List of dictionaries containing failed URL details
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"ical_validation_report_{timestamp}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as file:
        file.write("iCalendar URL Validation Report\n")
        file.write("==============================\n\n")
        file.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        file.write(f"Total URLs checked: {total_urls}\n")
        file.write(f"Working URLs: {working_urls} ({working_urls/total_urls*100:.1f}%)\n")
        file.write(f"Failed URLs: {len(failed_urls)} ({len(failed_urls)/total_urls*100:.1f}%)\n\n")
        
        if failed_urls:
            file.write("Failed URLs Details:\n")
            file.write("--------------------\n\n")
            
            for i, entry in enumerate(failed_urls, 1):
                file.write(f"{i}. ID: {entry['ID']}, Property ID: {entry['Property ID']}\n")
                file.write(f"   URL: {entry['URL']}\n")
                file.write(f"   Error: {entry['Error']}\n\n")
        
        file.write("\nEnd of Report")
    
    print(f"\nValidation complete!")
    print(f"Total URLs: {total_urls}")
    print(f"Working URLs: {working_urls}")
    print(f"Failed URLs: {len(failed_urls)}")
    print(f"Report saved to: {report_file}")
    
    # Also save failed URLs to CSV for easier processing
    if failed_urls:
        csv_report = f"failed_ical_urls_{timestamp}.csv"
        with open(csv_report, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=['ID', 'Property ID', 'URL', 'Error'])
            writer.writeheader()
            writer.writerows(failed_urls)
        print(f"Failed URLs CSV saved to: {csv_report}")

if __name__ == "__main__":
    validate_ical_urls("ical.csv")