#!/usr/bin/env python3
"""
Analytics Demo - Showcasing the power of SQLite-based iCalendar analytics
"""

from ical_analytics import ICalAnalytics
from ical_database_sync import ICalDatabaseSync
import json

def main():
    print("=== iCalendar Analytics Demo ===")
    print("=" * 35)
    
    # Initialize analytics
    analytics = ICalAnalytics("ical_data.db")
    
    # Let's sync a few more properties to get better data
    print("\n🔄 Syncing more properties for better analytics...")
    sync_system = ICalDatabaseSync("ical_data.db")
    
    # Sync first 10 properties
    import sqlite3
    conn = sqlite3.connect("ical_data.db")
    cursor = conn.cursor()
    cursor.execute("SELECT property_id FROM properties WHERE sync_status != 'success' LIMIT 10")
    properties_to_sync = [row[0] for row in cursor.fetchall()]
    conn.close()
    
    for i, property_id in enumerate(properties_to_sync, 1):
        print(f"  Syncing {i}/{len(properties_to_sync)}: Property {property_id}")
        result = sync_system.sync_property(property_id)
        if result['status'] == 'success':
            print(f"    ✅ {result['events_processed']} events")
        else:
            print(f"    ❌ Failed: {result['error'][:50]}...")
    
    print("\n📊 Analytics Results:")
    print("=" * 25)
    
    # 1. Overall Occupancy Analysis
    print("\n1️⃣ Overall Occupancy Analysis:")
    occupancy = analytics.get_occupancy_analysis()
    
    overall = occupancy['overall']
    print(f"   Total Days Analyzed: {overall['total_days']}")
    print(f"   Occupied Days: {overall['occupied_days']}")
    print(f"   Overall Occupancy Rate: {overall['occupancy_rate']}%")
    
    # Top performing properties
    print(f"\n   🏆 Top 5 Properties by Occupancy:")
    for i, prop in enumerate(occupancy['by_property'][:5], 1):
        print(f"   {i}. Property {prop['property_id']} ({prop['platform']}): {prop['occupancy_rate']}%")
    
    # Platform comparison
    print(f"\n   📱 Platform Performance:")
    for platform in occupancy['by_platform']:
        print(f"   {platform['platform'].title()}: {platform['occupancy_rate']}% occupancy")
    
    # 2. Booking Patterns Analysis
    print("\n2️⃣ Booking Patterns Analysis:")
    patterns = analytics.get_booking_patterns()
    
    if 'error' not in patterns:
        duration = patterns['duration_stats']
        print(f"   Average Stay Duration: {duration['avg_duration']} days")
        print(f"   Median Stay Duration: {duration['median_duration']} days")
        print(f"   Stay Range: {duration['min_duration']} - {duration['max_duration']} days")
        print(f"   Total Bookings Analyzed: {patterns['total_bookings']}")
        
        # Seasonal patterns
        if patterns['seasonal_patterns']['monthly']:
            print(f"\n   📅 Monthly Booking Distribution:")
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            for month_num, count in patterns['seasonal_patterns']['monthly'].items():
                print(f"   {months[month_num-1]}: {count} bookings")
        
        # Platform distribution
        if patterns['platform_distribution']:
            print(f"\n   🏢 Bookings by Platform:")
            for platform, count in patterns['platform_distribution'].items():
                print(f"   {platform.title()}: {count} bookings")
    else:
        print("   No booking data available yet")
    
    # 3. Availability Gaps (Revenue Opportunities)
    print("\n3️⃣ Revenue Opportunities (Availability Gaps):")
    gaps = analytics.find_availability_gaps(min_gap_days=3)
    
    if gaps:
        print(f"   Found {len(gaps)} availability gaps of 3+ days")
        print(f"   🎯 Top 5 Opportunities:")
        for i, gap in enumerate(gaps[:5], 1):
            print(f"   {i}. Property {gap['property_id']} ({gap['platform']}): {gap['gap_days']} days")
            print(f"      Available: {gap['start_date']} to {gap['end_date']}")
    else:
        print("   No significant availability gaps found")
    
    # 4. Conflict Detection
    print("\n4️⃣ Booking Conflicts Detection:")
    conflicts = analytics.detect_conflicts()
    
    if conflicts:
        print(f"   ⚠️  Found {len(conflicts)} potential conflicts!")
        for i, conflict in enumerate(conflicts[:3], 1):
            print(f"   {i}. Property {conflict['property_id']}:")
            print(f"      {conflict['platform1']}: {conflict['start1']} to {conflict['end1']}")
            print(f"      {conflict['platform2']}: {conflict['start2']} to {conflict['end2']}")
    else:
        print("   ✅ No booking conflicts detected")
    
    # 5. Database Insights
    print("\n5️⃣ Database Insights:")
    conn = sqlite3.connect("ical_data.db")
    cursor = conn.cursor()
    
    # Sync status
    cursor.execute("SELECT sync_status, COUNT(*) FROM properties GROUP BY sync_status")
    sync_stats = dict(cursor.fetchall())
    print(f"   Sync Status: {sync_stats}")
    
    # Event types
    cursor.execute("SELECT event_type, COUNT(*) FROM calendar_events GROUP BY event_type")
    event_stats = dict(cursor.fetchall())
    print(f"   Event Types: {event_stats}")
    
    # Date range
    cursor.execute("SELECT MIN(start_date), MAX(end_date) FROM calendar_events")
    date_range = cursor.fetchone()
    print(f"   Data Range: {date_range[0]} to {date_range[1]}")
    
    conn.close()
    
    print("\n🎉 Analytics Demo Complete!")
    print("\n💡 What you can do next:")
    print("   - Sync all properties: sync_system.sync_all_properties()")
    print("   - Generate full report: analytics.generate_report('report.json')")
    print("   - Build custom dashboards using the SQLite database")
    print("   - Set up automated sync schedules")
    print("   - Create alerts for conflicts and opportunities")

if __name__ == "__main__":
    main()
