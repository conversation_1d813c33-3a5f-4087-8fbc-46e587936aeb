#!/usr/bin/env python3
"""
Initialize the database with the required schema.
"""
import sqlite3
import os

def init_db():
    # Read the schema file
    with open('database_schema.sql', 'r') as f:
        schema = f.read()
    
    # Connect to the database (this will create it if it doesn't exist)
    conn = sqlite3.connect('ical_data.db')
    
    try:
        # Execute the schema SQL
        conn.executescript(schema)
        conn.commit()
        print("✅ Database initialized successfully!")
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    print("Initializing database...")
    init_db()
