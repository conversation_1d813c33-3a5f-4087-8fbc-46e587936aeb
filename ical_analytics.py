import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

class ICalAnalytics:
    """
    Advanced analytics module for iCalendar data stored in SQLite.
    Provides occupancy analysis, revenue insights, and booking patterns.
    """

    def __init__(self, db_path: str = "ical_data.db"):
        self.db_path = db_path

    def get_occupancy_analysis(self,
                             start_date: str = None,
                             end_date: str = None,
                             platform: str = None) -> Dict:
        """
        Comprehensive occupancy analysis with filtering options.

        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            platform: Filter by platform (airbnb, booking, etc.)
        """
        conn = sqlite3.connect(self.db_path)

        # Build query with filters
        where_conditions = []
        params = []

        if start_date:
            where_conditions.append("date >= ?")
            params.append(start_date)

        if end_date:
            where_conditions.append("date <= ?")
            params.append(end_date)

        if platform:
            where_conditions.append("platform = ?")
            params.append(platform)

        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # Overall occupancy stats with corrected logic
        query = f"""
            SELECT
                COUNT(*) as total_days,
                SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) as available_days,
                SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) as booked_days,
                SUM(CASE WHEN event_type = 'blocked' THEN 1 ELSE 0 END) as blocked_days,
                SUM(CASE WHEN NOT is_available AND event_type != 'booking' THEN 1 ELSE 0 END) as other_unavailable_days,
                ROUND(
                    CASE
                        WHEN (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END)) > 0
                        THEN (SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) * 100.0) /
                             (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END))
                        ELSE 0
                    END,
                    2
                ) as true_occupancy_rate,
                ROUND(
                    (SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) * 100.0) / COUNT(*),
                    2
                ) as calendar_blocked_rate
            FROM daily_availability
            {where_clause}
        """

        overall_stats = pd.read_sql_query(query, conn, params=params).iloc[0].to_dict()

        # Occupancy by property with corrected logic
        query = f"""
            SELECT
                property_id,
                platform,
                COUNT(*) as total_days,
                SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) as available_days,
                SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) as booked_days,
                SUM(CASE WHEN event_type = 'blocked' THEN 1 ELSE 0 END) as blocked_days,
                ROUND(
                    CASE
                        WHEN (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END)) > 0
                        THEN (SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) * 100.0) /
                             (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END))
                        ELSE 0
                    END,
                    2
                ) as true_occupancy_rate,
                ROUND(
                    (SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) * 100.0) / COUNT(*),
                    2
                ) as calendar_blocked_rate
            FROM daily_availability
            {where_clause}
            GROUP BY property_id, platform
            ORDER BY true_occupancy_rate DESC
        """

        property_stats = pd.read_sql_query(query, conn, params=params)

        # Occupancy by platform with corrected logic
        query = f"""
            SELECT
                platform,
                COUNT(*) as total_days,
                SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) as available_days,
                SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) as booked_days,
                SUM(CASE WHEN event_type = 'blocked' THEN 1 ELSE 0 END) as blocked_days,
                ROUND(
                    CASE
                        WHEN (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END)) > 0
                        THEN (SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) * 100.0) /
                             (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END))
                        ELSE 0
                    END,
                    2
                ) as true_occupancy_rate,
                ROUND(
                    (SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) * 100.0) / COUNT(*),
                    2
                ) as calendar_blocked_rate
            FROM daily_availability
            {where_clause}
            GROUP BY platform
            ORDER BY true_occupancy_rate DESC
        """

        platform_stats = pd.read_sql_query(query, conn, params=params)

        conn.close()

        return {
            'overall': overall_stats,
            'by_property': property_stats.to_dict('records'),
            'by_platform': platform_stats.to_dict('records'),
            'filters_applied': {
                'start_date': start_date,
                'end_date': end_date,
                'platform': platform
            }
        }

    def get_booking_patterns(self, property_id: str = None) -> Dict:
        """
        Analyze booking patterns including seasonality and lead times.
        """
        conn = sqlite3.connect(self.db_path)

        where_clause = "WHERE event_type = 'booking'"
        params = []

        if property_id:
            where_clause += " AND property_id = ?"
            params.append(property_id)

        # Booking duration analysis
        query = f"""
            SELECT
                property_id,
                platform,
                start_date,
                end_date,
                julianday(end_date) - julianday(start_date) as duration_days
            FROM calendar_events
            {where_clause}
            ORDER BY start_date
        """

        bookings_df = pd.read_sql_query(query, conn, params=params)

        if bookings_df.empty:
            conn.close()
            return {'error': 'No booking data found'}

        # Convert dates
        bookings_df['start_date'] = pd.to_datetime(bookings_df['start_date'])
        bookings_df['end_date'] = pd.to_datetime(bookings_df['end_date'])
        bookings_df['month'] = bookings_df['start_date'].dt.month
        bookings_df['day_of_week'] = bookings_df['start_date'].dt.dayofweek
        bookings_df['quarter'] = bookings_df['start_date'].dt.quarter

        # Duration statistics
        duration_stats = {
            'avg_duration': round(bookings_df['duration_days'].mean(), 2),
            'median_duration': round(bookings_df['duration_days'].median(), 2),
            'min_duration': int(bookings_df['duration_days'].min()),
            'max_duration': int(bookings_df['duration_days'].max())
        }

        # Seasonal patterns
        monthly_bookings = bookings_df.groupby('month').size().to_dict()
        quarterly_bookings = bookings_df.groupby('quarter').size().to_dict()

        # Day of week patterns
        dow_bookings = bookings_df.groupby('day_of_week').size().to_dict()
        dow_names = {0: 'Monday', 1: 'Tuesday', 2: 'Wednesday', 3: 'Thursday',
                    4: 'Friday', 5: 'Saturday', 6: 'Sunday'}
        dow_bookings_named = {dow_names[k]: v for k, v in dow_bookings.items()}

        # Platform distribution
        platform_bookings = bookings_df.groupby('platform').size().to_dict()

        conn.close()

        return {
            'duration_stats': duration_stats,
            'seasonal_patterns': {
                'monthly': monthly_bookings,
                'quarterly': quarterly_bookings
            },
            'day_of_week_patterns': dow_bookings_named,
            'platform_distribution': platform_bookings,
            'total_bookings': len(bookings_df)
        }

    def find_availability_gaps(self, min_gap_days: int = 3) -> List[Dict]:
        """
        Find availability gaps that could represent revenue opportunities.
        """
        conn = sqlite3.connect(self.db_path)

        query = """
            SELECT property_id, platform, date, is_available
            FROM daily_availability
            WHERE is_available = 1
            ORDER BY property_id, platform, date
        """

        availability_df = pd.read_sql_query(query, conn)
        conn.close()

        if availability_df.empty:
            return []

        availability_df['date'] = pd.to_datetime(availability_df['date'])
        gaps = []

        for (property_id, platform), group in availability_df.groupby(['property_id', 'platform']):
            group = group.sort_values('date')

            # Find consecutive available periods
            group['date_diff'] = group['date'].diff().dt.days
            group['gap_start'] = group['date_diff'] > 1
            group['gap_id'] = group['gap_start'].cumsum()

            # Analyze each gap
            for gap_id, gap_group in group.groupby('gap_id'):
                gap_length = len(gap_group)

                if gap_length >= min_gap_days:
                    gaps.append({
                        'property_id': property_id,
                        'platform': platform,
                        'start_date': gap_group['date'].min().strftime('%Y-%m-%d'),
                        'end_date': gap_group['date'].max().strftime('%Y-%m-%d'),
                        'gap_days': gap_length
                    })

        return sorted(gaps, key=lambda x: x['gap_days'], reverse=True)

    def detect_conflicts(self) -> List[Dict]:
        """
        Detect potential booking conflicts across platforms.
        """
        conn = sqlite3.connect(self.db_path)

        query = """
            SELECT
                ce1.property_id,
                ce1.platform as platform1,
                ce1.start_date as start1,
                ce1.end_date as end1,
                ce1.event_type as type1,
                ce2.platform as platform2,
                ce2.start_date as start2,
                ce2.end_date as end2,
                ce2.event_type as type2
            FROM calendar_events ce1
            JOIN calendar_events ce2 ON ce1.property_id = ce2.property_id
            WHERE ce1.platform != ce2.platform
            AND ce1.event_type IN ('booking', 'blocked')
            AND ce2.event_type IN ('booking', 'blocked')
            AND (
                (ce1.start_date <= ce2.start_date AND ce1.end_date > ce2.start_date)
                OR (ce2.start_date <= ce1.start_date AND ce2.end_date > ce1.start_date)
            )
            ORDER BY ce1.property_id, ce1.start_date
        """

        conflicts_df = pd.read_sql_query(query, conn)
        conn.close()

        return conflicts_df.to_dict('records')

    def get_property_stats(self, property_id: str = None) -> Dict:
        """
        Get statistics for properties - either overall or for a specific property.

        Args:
            property_id: Optional property ID for specific property stats

        Returns:
            Dictionary with property statistics
        """
        conn = sqlite3.connect(self.db_path)

        if property_id:
            # Stats for specific property
            cursor = conn.cursor()

            # Get property summary
            cursor.execute("""
                SELECT p.property_id, p.platform, p.last_sync, p.sync_status,
                       COUNT(DISTINCT ce.id) as total_events,
                       COUNT(DISTINCT CASE WHEN ce.event_type = 'booking' THEN ce.id END) as bookings,
                       COUNT(DISTINCT CASE WHEN ce.event_type = 'blocked' THEN ce.id END) as blocked_periods,
                       MIN(ce.start_date) as earliest_event,
                       MAX(ce.end_date) as latest_event
                FROM properties p
                LEFT JOIN calendar_events ce ON p.property_id = ce.property_id
                WHERE p.property_id = ?
                GROUP BY p.property_id, p.platform, p.last_sync, p.sync_status
            """, (property_id,))
            property_summary = cursor.fetchone()

            # Get enhanced occupancy stats with corrected logic
            cursor.execute("""
                SELECT property_id, platform,
                       COUNT(*) as total_days,
                       SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) as available_days,
                       SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) as booked_days,
                       SUM(CASE WHEN event_type = 'blocked' THEN 1 ELSE 0 END) as blocked_days,
                       SUM(CASE WHEN NOT is_available AND event_type != 'booking' THEN 1 ELSE 0 END) as other_unavailable_days,
                       ROUND(
                           CASE
                               WHEN (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END)) > 0
                               THEN (SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END) * 100.0) /
                                    (SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) + SUM(CASE WHEN event_type = 'booking' THEN 1 ELSE 0 END))
                               ELSE 0
                           END,
                           2
                       ) as true_occupancy_rate,
                       ROUND(
                           (SUM(CASE WHEN NOT is_available THEN 1 ELSE 0 END) * 100.0) / COUNT(*),
                           2
                       ) as calendar_blocked_rate,
                       MIN(date) as period_start,
                       MAX(date) as period_end
                FROM daily_availability
                WHERE property_id = ?
                GROUP BY property_id, platform
            """, (property_id,))
            occupancy_stats = cursor.fetchone()

            conn.close()
            return {
                'property_summary': property_summary,
                'occupancy_stats': occupancy_stats
            }
        else:
            # Overall stats
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM properties")
            total_properties = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM properties WHERE sync_status = 'success'")
            synced_properties = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM calendar_events")
            total_events = cursor.fetchone()[0]

            cursor.execute("SELECT platform, COUNT(*) FROM properties GROUP BY platform")
            platform_counts = dict(cursor.fetchall())

            conn.close()
            return {
                'total_properties': total_properties,
                'synced_properties': synced_properties,
                'total_events': total_events,
                'platform_distribution': platform_counts
            }

    def generate_report(self, output_file: str = None) -> Dict:
        """
        Generate a comprehensive analytics report.
        """
        report = {
            'generated_at': datetime.now().isoformat(),
            'occupancy_analysis': self.get_occupancy_analysis(),
            'booking_patterns': self.get_booking_patterns(),
            'availability_gaps': self.find_availability_gaps(),
            'conflicts': self.detect_conflicts()
        }

        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)

        return report
