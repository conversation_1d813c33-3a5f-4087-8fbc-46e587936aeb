#!/usr/bin/env python3
"""
Test script to debug booking patterns functionality
"""

import sqlite3
import pandas as pd
from datetime import datetime

def test_booking_patterns():
    """Test the booking patterns analysis directly"""
    
    print("🔍 TESTING BOOKING PATTERNS")
    print("=" * 35)
    
    db_path = 'ical_data.db'
    conn = sqlite3.connect(db_path)
    
    # Check if we have booking events
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM calendar_events WHERE event_type = 'booking'")
    booking_count = cursor.fetchone()[0]
    print(f"📊 Total booking events: {booking_count}")
    
    if booking_count == 0:
        print("❌ No booking events found!")
        conn.close()
        return
    
    # Test the booking patterns query
    query = """
        SELECT
            property_id,
            platform,
            start_date,
            end_date,
            julianday(end_date) - julianday(start_date) as duration_days
        FROM calendar_events
        WHERE event_type = 'booking'
        ORDER BY start_date
        LIMIT 10
    """
    
    print(f"\n📋 Sample booking events:")
    cursor.execute(query)
    sample_bookings = cursor.fetchall()
    
    for booking in sample_bookings:
        print(f"  Property {booking[0]} ({booking[1]}): {booking[2]} to {booking[3]} ({booking[4]} days)")
    
    # Test pandas processing
    try:
        print(f"\n🔄 Testing pandas processing...")
        
        full_query = """
            SELECT
                property_id,
                platform,
                start_date,
                end_date,
                julianday(end_date) - julianday(start_date) as duration_days
            FROM calendar_events
            WHERE event_type = 'booking'
            ORDER BY start_date
        """
        
        bookings_df = pd.read_sql_query(full_query, conn)
        print(f"✅ Loaded {len(bookings_df)} bookings into DataFrame")
        
        if not bookings_df.empty:
            # Convert dates
            bookings_df['start_date'] = pd.to_datetime(bookings_df['start_date'])
            bookings_df['end_date'] = pd.to_datetime(bookings_df['end_date'])
            bookings_df['month'] = bookings_df['start_date'].dt.month
            bookings_df['day_of_week'] = bookings_df['start_date'].dt.dayofweek
            bookings_df['quarter'] = bookings_df['start_date'].dt.quarter
            
            print(f"✅ Date conversion successful")
            
            # Duration statistics
            duration_stats = {
                'avg_duration': round(bookings_df['duration_days'].mean(), 2),
                'median_duration': round(bookings_df['duration_days'].median(), 2),
                'min_duration': int(bookings_df['duration_days'].min()),
                'max_duration': int(bookings_df['duration_days'].max())
            }
            
            print(f"📊 Duration stats: {duration_stats}")
            
            # Seasonal patterns
            monthly_bookings = bookings_df.groupby('month').size().to_dict()
            print(f"📅 Monthly bookings: {monthly_bookings}")
            
            # Platform distribution
            platform_bookings = bookings_df.groupby('platform').size().to_dict()
            print(f"🏢 Platform distribution: {platform_bookings}")
            
            result = {
                'duration_stats': duration_stats,
                'seasonal_patterns': {
                    'monthly': monthly_bookings,
                    'quarterly': bookings_df.groupby('quarter').size().to_dict()
                },
                'day_of_week_patterns': {},
                'platform_distribution': platform_bookings,
                'total_bookings': len(bookings_df)
            }
            
            print(f"\n✅ SUCCESS! Booking patterns result:")
            print(f"  Total bookings: {result['total_bookings']}")
            print(f"  Average duration: {result['duration_stats']['avg_duration']} days")
            print(f"  Monthly data: {result['seasonal_patterns']['monthly']}")
            print(f"  Platform data: {result['platform_distribution']}")
            
            # Check if monthly data exists for chart
            monthly_data = result['seasonal_patterns']['monthly']
            if monthly_data:
                print(f"\n📊 CHART DATA AVAILABLE:")
                month_names = {1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun',
                              7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'}
                for month, count in monthly_data.items():
                    month_name = month_names.get(int(month), f'Month {month}')
                    print(f"  {month_name}: {count} bookings")
            else:
                print(f"\n❌ NO CHART DATA - monthly_data is empty")
        
    except Exception as e:
        print(f"❌ Error in pandas processing: {e}")
        import traceback
        traceback.print_exc()
    
    conn.close()

if __name__ == "__main__":
    test_booking_patterns()
