import sqlite3
import csv
import requests
from datetime import datetime, timedelta
from urllib.parse import urlparse
import re
from typing import Dict, List, Tuple, Optional
import logging
import urllib3

# Disable SSL warnings for development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ICalDatabaseSync:
    """
    Comprehensive iCalendar data extraction and SQLite database synchronization system.
    """

    def __init__(self, db_path: str = "ical_data.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize the SQLite database with the schema."""
        try:
            with open('database_schema.sql', 'r') as f:
                schema = f.read()

            conn = sqlite3.connect(self.db_path)
            conn.executescript(schema)
            conn.commit()
            conn.close()
            logger.info(f"Database initialized: {self.db_path}")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise

    def load_properties_from_csv(self, csv_file: str):
        """Load properties from CSV file into the database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        added_count = 0
        updated_count = 0

        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)

                for row in reader:
                    property_id = row['Property ID']
                    ical_url = row['Icalendar URL'].strip()
                    platform = self.detect_platform(ical_url)

                    # Check if property exists
                    cursor.execute(
                        "SELECT id FROM properties WHERE property_id = ?",
                        (property_id,)
                    )

                    if cursor.fetchone():
                        # Update existing property
                        cursor.execute("""
                            UPDATE properties
                            SET ical_url = ?, platform = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE property_id = ?
                        """, (ical_url, platform, property_id))
                        updated_count += 1
                    else:
                        # Insert new property
                        cursor.execute("""
                            INSERT INTO properties (property_id, ical_url, platform)
                            VALUES (?, ?, ?)
                        """, (property_id, ical_url, platform))
                        added_count += 1

            conn.commit()
            logger.info(f"Properties loaded: {added_count} added, {updated_count} updated")

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to load properties: {e}")
            raise
        finally:
            conn.close()

    def detect_platform(self, url: str) -> str:
        """Detect the platform based on URL."""
        domain = urlparse(url).netloc.lower()

        if 'airbnb.com' in domain:
            return 'airbnb'
        elif 'booking.com' in domain:
            return 'booking'
        elif 'lodgify.com' in domain:
            return 'lodgify'
        elif 'darent.com' in domain:
            return 'darent'
        elif 'gathern.co' in domain:
            return 'gathern'
        elif 'guesty.com' in domain:
            return 'guesty'
        elif 'agoda.com' in domain:
            return 'agoda'
        else:
            return 'unknown'

    def parse_ical_content(self, content: str, property_id: str, platform: str) -> List[Dict]:
        """Parse iCalendar content and extract events."""
        events = []
        current_event = {}
        in_event = False

        lines = content.strip().split('\n')

        for line in lines:
            line = line.strip()

            if line == 'BEGIN:VEVENT':
                in_event = True
                current_event = {'property_id': property_id, 'platform': platform}
            elif line == 'END:VEVENT' and in_event:
                if self.is_valid_event(current_event):
                    # Determine event type
                    current_event['event_type'] = self.determine_event_type(
                        current_event.get('summary', ''),
                        current_event.get('description', '')
                    )
                    events.append(current_event.copy())
                in_event = False
                current_event = {}
            elif in_event and ':' in line:
                key, value = line.split(':', 1)

                # Handle different iCal properties
                if key == 'UID':
                    current_event['event_uid'] = value
                elif key.startswith('DTSTART'):
                    current_event['start_date'] = self.parse_date(value)
                elif key.startswith('DTEND'):
                    current_event['end_date'] = self.parse_date(value)
                elif key == 'SUMMARY':
                    current_event['summary'] = value
                elif key == 'DESCRIPTION':
                    current_event['description'] = value
                    # Extract Airbnb-specific data
                    if platform == 'airbnb':
                        self.extract_airbnb_details(value, current_event)
                elif key == 'DTSTAMP':
                    current_event['dtstamp'] = self.parse_datetime(value)

        return events

    def is_valid_event(self, event: Dict) -> bool:
        """Check if event has required fields."""
        required_fields = ['event_uid', 'start_date', 'end_date']
        return all(field in event for field in required_fields)

    def determine_event_type(self, summary: str, description: str) -> str:
        """
        Determine the type of event based on summary and description.

        Returns:
            'booking' - Actual guest booking (counts as occupied)
            'blocked' - Property blocked/unavailable (maintenance, owner use, etc.)
            'unavailable' - General unavailability (fallback)
        """
        summary_lower = summary.lower()
        description_lower = description.lower() if description else ""

        # Actual booking indicators
        booking_indicators = [
            'reserved', 'booking', 'booked', 'guest', 'confirmed',
            'check-in', 'checkout', 'reservation'
        ]

        # Blocked/unavailable indicators (not actual bookings)
        blocked_indicators = [
            'not available', 'blocked', 'maintenance', 'owner use',
            'airbnb (not available)', 'unavailable', 'closed',
            'out of service', 'private use', 'cleaning'
        ]

        # Check for actual bookings first
        for indicator in booking_indicators:
            if indicator in summary_lower or indicator in description_lower:
                return 'booking'

        # Check for blocked/unavailable periods
        for indicator in blocked_indicators:
            if indicator in summary_lower or indicator in description_lower:
                return 'blocked'

        # If summary contains what looks like a guest name (not system text)
        # and doesn't match blocked indicators, likely a booking
        if (summary and
            not any(blocked in summary_lower for blocked in blocked_indicators) and
            len(summary.strip()) > 3 and
            not summary_lower.startswith(('airbnb', 'booking.com', 'vrbo'))):
            return 'booking'

        # Default to blocked for safety (don't inflate occupancy)
        return 'blocked'

    def parse_date(self, date_str: str) -> str:
        """Parse iCal date format to YYYY-MM-DD."""
        # Remove VALUE=DATE: prefix if present
        if ';VALUE=DATE:' in date_str:
            date_str = date_str.split(':')[1]
        elif ':' in date_str:
            date_str = date_str.split(':')[1]

        # Parse YYYYMMDD format
        if len(date_str) >= 8:
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        return date_str

    def parse_datetime(self, datetime_str: str) -> str:
        """Parse iCal datetime format."""
        if ':' in datetime_str:
            datetime_str = datetime_str.split(':')[1]

        # Parse YYYYMMDDTHHMMSSZ format
        if 'T' in datetime_str:
            date_part = datetime_str.split('T')[0]
            time_part = datetime_str.split('T')[1].replace('Z', '')
            return f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]} {time_part[:2]}:{time_part[2:4]}:{time_part[4:6]}"

        return datetime_str

    def extract_airbnb_details(self, description: str, event: Dict):
        """Extract Airbnb-specific details from description."""
        if 'Reservation URL:' in description:
            url_match = re.search(r'Reservation URL: (https://[^\s\\]+)', description)
            if url_match:
                event['reservation_url'] = url_match.group(1)

        if 'Phone Number (Last 4 Digits):' in description:
            phone_match = re.search(r'Phone Number \(Last 4 Digits\): (\d{4})', description)
            if phone_match:
                event['guest_phone_last4'] = phone_match.group(1)

    def sync_property(self, property_id: str) -> Dict:
        """Sync a single property's calendar data."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        sync_start = datetime.now()
        result = {
            'status': 'failed',
            'events_processed': 0,
            'events_added': 0,
            'events_updated': 0,
            'events_deleted': 0,
            'error': None
        }

        try:
            # Get property details
            cursor.execute(
                "SELECT ical_url, platform FROM properties WHERE property_id = ?",
                (property_id,)
            )
            property_data = cursor.fetchone()

            if not property_data:
                raise ValueError(f"Property {property_id} not found")

            ical_url, platform = property_data

            # Fetch iCal content with rate limiting
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            }

            response = requests.get(ical_url, headers=headers, timeout=30, verify=False)
            response.raise_for_status()

            if not response.text.strip().startswith('BEGIN:VCALENDAR'):
                raise ValueError("Invalid iCalendar format")

            # Parse events
            events = self.parse_ical_content(response.text, property_id, platform)
            result['events_processed'] = len(events)

            # Get existing events for comparison
            cursor.execute(
                "SELECT event_uid FROM calendar_events WHERE property_id = ? AND platform = ?",
                (property_id, platform)
            )
            existing_uids = {row[0] for row in cursor.fetchall()}

            # Process new/updated events
            new_uids = set()
            for event in events:
                new_uids.add(event['event_uid'])

                # Check if event exists
                cursor.execute(
                    "SELECT id FROM calendar_events WHERE property_id = ? AND event_uid = ? AND platform = ?",
                    (property_id, event['event_uid'], platform)
                )

                if cursor.fetchone():
                    # Update existing event
                    cursor.execute("""
                        UPDATE calendar_events SET
                            start_date = ?, end_date = ?, summary = ?, description = ?,
                            event_type = ?, reservation_url = ?, guest_phone_last4 = ?,
                            dtstamp = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE property_id = ? AND event_uid = ? AND platform = ?
                    """, (
                        event['start_date'], event['end_date'], event.get('summary'),
                        event.get('description'), event['event_type'],
                        event.get('reservation_url'), event.get('guest_phone_last4'),
                        event.get('dtstamp'), property_id, event['event_uid'], platform
                    ))
                    result['events_updated'] += 1
                else:
                    # Insert new event
                    cursor.execute("""
                        INSERT INTO calendar_events (
                            property_id, event_uid, start_date, end_date, summary,
                            description, event_type, platform, reservation_url,
                            guest_phone_last4, dtstamp
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        property_id, event['event_uid'], event['start_date'],
                        event['end_date'], event.get('summary'), event.get('description'),
                        event['event_type'], platform, event.get('reservation_url'),
                        event.get('guest_phone_last4'), event.get('dtstamp')
                    ))
                    result['events_added'] += 1

            # Delete events that no longer exist
            deleted_uids = existing_uids - new_uids
            if deleted_uids:
                placeholders = ','.join(['?' for _ in deleted_uids])
                cursor.execute(f"""
                    DELETE FROM calendar_events
                    WHERE property_id = ? AND platform = ? AND event_uid IN ({placeholders})
                """, [property_id, platform] + list(deleted_uids))
                result['events_deleted'] = cursor.rowcount

            # Update daily availability summary
            self.update_daily_availability(property_id, platform, cursor)

            # Update property sync status
            cursor.execute("""
                UPDATE properties SET
                    last_sync = CURRENT_TIMESTAMP,
                    sync_status = 'success',
                    sync_error = NULL,
                    updated_at = CURRENT_TIMESTAMP
                WHERE property_id = ?
            """, (property_id,))

            result['status'] = 'success'
            conn.commit()

        except Exception as e:
            conn.rollback()
            result['error'] = str(e)
            logger.error(f"Sync failed for property {property_id}: {e}")

            # Update property sync status
            cursor.execute("""
                UPDATE properties SET
                    sync_status = 'failed',
                    sync_error = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE property_id = ?
            """, (str(e), property_id))
            conn.commit()

        finally:
            # Record sync history
            sync_end = datetime.now()
            cursor.execute("""
                INSERT INTO sync_history (
                    property_id, sync_start, sync_end, status,
                    events_processed, events_added, events_updated, events_deleted,
                    error_message
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                property_id, sync_start, sync_end, result['status'],
                result['events_processed'], result['events_added'],
                result['events_updated'], result['events_deleted'],
                result['error']
            ))
            conn.commit()
            conn.close()

        return result

    def update_daily_availability(self, property_id: str, platform: str, cursor):
        """Update the daily availability summary table."""
        # Clear existing daily availability for this property/platform
        cursor.execute(
            "DELETE FROM daily_availability WHERE property_id = ? AND platform = ?",
            (property_id, platform)
        )

        # Get all events for this property
        cursor.execute("""
            SELECT start_date, end_date, event_type
            FROM calendar_events
            WHERE property_id = ? AND platform = ?
            ORDER BY start_date
        """, (property_id, platform))

        events = cursor.fetchall()

        if not events:
            return

        # Find date range
        min_date = min(event[0] for event in events)
        max_date = max(event[1] for event in events)

        # Generate daily availability records
        current_date = datetime.strptime(min_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(max_date, '%Y-%m-%d').date()

        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            is_available = True
            event_type = None

            # Check if this date is covered by any event
            for start_date, end_date_event, event_type_event in events:
                start = datetime.strptime(start_date, '%Y-%m-%d').date()
                end = datetime.strptime(end_date_event, '%Y-%m-%d').date()

                if start <= current_date < end:
                    is_available = False
                    event_type = event_type_event
                    break

            cursor.execute("""
                INSERT INTO daily_availability (property_id, date, is_available, event_type, platform)
                VALUES (?, ?, ?, ?, ?)
            """, (property_id, date_str, is_available, event_type, platform))

            current_date += timedelta(days=1)

    def generate_daily_availability(self, property_id: str, platform: str):
        """Generate daily availability data for a specific property."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # Get all events for this property
            cursor.execute("""
                SELECT start_date, end_date, event_type
                FROM calendar_events
                WHERE property_id = ? AND platform = ?
                ORDER BY start_date
            """, (property_id, platform))

            events = cursor.fetchall()

            if not events:
                return

            # Find date range
            min_date = min(event[0] for event in events)
            max_date = max(event[1] for event in events)

            # Delete existing daily availability for this property
            cursor.execute("""
                DELETE FROM daily_availability
                WHERE property_id = ? AND platform = ?
            """, (property_id, platform))

            # Generate daily availability records
            current_date = datetime.strptime(min_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(max_date, '%Y-%m-%d').date()

            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                is_available = True
                event_type = None

                # Check if this date is covered by any event
                for start_date, end_date_event, event_type_event in events:
                    start = datetime.strptime(start_date, '%Y-%m-%d').date()
                    end = datetime.strptime(end_date_event, '%Y-%m-%d').date()

                    if start <= current_date < end:
                        is_available = False
                        event_type = event_type_event
                        break

                cursor.execute("""
                    INSERT INTO daily_availability (property_id, date, is_available, event_type, platform)
                    VALUES (?, ?, ?, ?, ?)
                """, (property_id, date_str, is_available, event_type, platform))

                current_date += timedelta(days=1)

            conn.commit()

        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()

    def sync_all_properties(self, max_workers: int = 5) -> Dict:
        """Sync all properties with rate limiting."""
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import time

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get all properties
        cursor.execute("SELECT property_id FROM properties ORDER BY last_sync ASC NULLS FIRST")
        property_ids = [row[0] for row in cursor.fetchall()]
        conn.close()

        results = {
            'total_properties': len(property_ids),
            'successful': 0,
            'failed': 0,
            'details': []
        }

        logger.info(f"Starting sync for {len(property_ids)} properties")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all sync tasks
            future_to_property = {
                executor.submit(self.sync_property, prop_id): prop_id
                for prop_id in property_ids
            }

            # Process completed tasks
            for future in as_completed(future_to_property):
                property_id = future_to_property[future]
                try:
                    result = future.result()
                    if result['status'] == 'success':
                        results['successful'] += 1
                        logger.info(f"✓ Property {property_id}: {result['events_processed']} events processed")
                    else:
                        results['failed'] += 1
                        logger.error(f"✗ Property {property_id}: {result['error']}")

                    results['details'].append({
                        'property_id': property_id,
                        'result': result
                    })

                except Exception as e:
                    results['failed'] += 1
                    logger.error(f"✗ Property {property_id}: Unexpected error: {e}")

                # Add small delay between requests
                time.sleep(0.2)

        logger.info(f"Sync completed: {results['successful']} successful, {results['failed']} failed")
        return results

    def get_property_stats(self, property_id: str = None) -> Dict:
        """Get statistics for properties."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if property_id:
            # Stats for specific property
            cursor.execute("""
                SELECT * FROM property_summary WHERE property_id = ?
            """, (property_id,))
            property_data = cursor.fetchone()

            if not property_data:
                return {'error': f'Property {property_id} not found'}

            # Get occupancy stats
            cursor.execute("""
                SELECT * FROM occupancy_stats WHERE property_id = ?
            """, (property_id,))
            occupancy_data = cursor.fetchone()

            conn.close()
            return {
                'property_summary': property_data,
                'occupancy_stats': occupancy_data
            }
        else:
            # Overall stats
            cursor.execute("SELECT COUNT(*) FROM properties")
            total_properties = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM properties WHERE sync_status = 'success'")
            synced_properties = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM calendar_events")
            total_events = cursor.fetchone()[0]

            cursor.execute("SELECT platform, COUNT(*) FROM properties GROUP BY platform")
            platform_counts = dict(cursor.fetchall())

            conn.close()
            return {
                'total_properties': total_properties,
                'synced_properties': synced_properties,
                'total_events': total_events,
                'platform_distribution': platform_counts
            }
