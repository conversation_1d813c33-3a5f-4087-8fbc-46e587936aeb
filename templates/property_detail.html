{% extends "base.html" %}

{% block title %}Property {{ property.property_id }} - iCalendar Analytics{% endblock %}
{% block page_title %}Property {{ property.property_id }} Details{% endblock %}

{% block content %}
<!-- Property Header -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h3 class="card-title">Property {{ property.property_id }}</h3>
                        <div class="mb-3">
                            <span class="badge bg-primary me-2">{{ property.platform|title }}</span>
                            <span class="sync-status {{ property.sync_status }}"></span>
                            <span class="badge bg-{{ 'success' if property.sync_status == 'success' else 'warning' if property.sync_status == 'pending' else 'danger' }}">
                                {{ property.sync_status|title }}
                            </span>
                        </div>
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-link me-1"></i>
                                <a href="{{ property.ical_url }}" target="_blank" class="text-decoration-none">
                                    iCalendar URL
                                </a>
                            </small>
                        </div>
                        {% if property.last_sync %}
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-clock me-1"></i>
                                Last synced: {{ property.last_sync }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="syncProperty()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Sync Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                {% if stats.occupancy_stats %}
                <div class="h2 text-primary">{{ "%.1f"|format(stats.occupancy_stats[4]) }}%</div>
                <div class="text-muted">Occupancy Rate</div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar" style="width: {{ stats.occupancy_stats[4] }}%"></div>
                </div>
                <small class="text-muted">
                    {{ stats.occupancy_stats[3] }}/{{ stats.occupancy_stats[2] }} days occupied
                </small>
                {% else %}
                <div class="h2 text-muted">No Data</div>
                <div class="text-muted">Sync property to see occupancy</div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
{% if stats.property_summary %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <div class="h4 text-white">{{ stats.property_summary[4] or 0 }}</div>
                <div class="text-white-50">Total Events</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <div class="h4 text-white">{{ stats.property_summary[5] or 0 }}</div>
                <div class="text-white-50">Bookings</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <div class="h4 text-white">{{ stats.property_summary[6] or 0 }}</div>
                <div class="text-white-50">Blocked Periods</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                {% if stats.property_summary[7] and stats.property_summary[8] %}
                <div class="h6 text-white">{{ stats.property_summary[7] }}</div>
                <div class="h6 text-white">to {{ stats.property_summary[8] }}</div>
                <div class="text-white-50 small">Date Range</div>
                {% else %}
                <div class="h4 text-white">-</div>
                <div class="text-white-50">No Events</div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Calendar Events -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Calendar Events
                </h5>
                <span class="badge bg-info">{{ events|length }} events</span>
            </div>
            <div class="card-body">
                {% if events %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Duration</th>
                                <th>Type</th>
                                <th>Summary</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for event in events %}
                            <tr>
                                <td>{{ event.start_date }}</td>
                                <td>{{ event.end_date }}</td>
                                <td>
                                    {% set duration = (event.end_date|strptime('%Y-%m-%d') - event.start_date|strptime('%Y-%m-%d')).days %}
                                    <span class="badge bg-secondary">{{ duration }} days</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if event.event_type == 'booking' else 'warning' if event.event_type == 'blocked' else 'secondary' }}">
                                        {{ event.event_type|title }}
                                    </span>
                                </td>
                                <td>{{ event.summary or '-' }}</td>
                                <td>
                                    {% if event.reservation_url %}
                                    <a href="{{ event.reservation_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    {% endif %}
                                    {% if event.guest_phone_last4 %}
                                    <small class="text-muted">Phone: ***{{ event.guest_phone_last4 }}</small>
                                    {% endif %}
                                    {% if event.description and event.description != event.summary %}
                                    <button class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="{{ event.description }}">
                                        <i class="fas fa-info"></i>
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <h5>No Events Found</h5>
                    <p>This property has no calendar events. Try syncing to fetch the latest data.</p>
                    <button class="btn btn-primary" onclick="syncProperty()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Sync Property
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Sync History -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Sync History
                </h5>
            </div>
            <div class="card-body">
                {% if sync_history %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Start Time</th>
                                <th>End Time</th>
                                <th>Status</th>
                                <th>Events Processed</th>
                                <th>Changes</th>
                                <th>Error</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sync in sync_history %}
                            <tr>
                                <td>
                                    <small>{{ sync.sync_start }}</small>
                                </td>
                                <td>
                                    {% if sync.sync_end %}
                                    <small>{{ sync.sync_end }}</small>
                                    {% else %}
                                    <small class="text-warning">Running...</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="sync-status {{ sync.status }}"></span>
                                    <span class="badge bg-{{ 'success' if sync.status == 'success' else 'danger' }}">
                                        {{ sync.status|title }}
                                    </span>
                                </td>
                                <td>{{ sync.events_processed or 0 }}</td>
                                <td>
                                    {% if sync.events_added or sync.events_updated or sync.events_deleted %}
                                    <div class="d-flex gap-1">
                                        {% if sync.events_added %}
                                        <span class="badge bg-success">+{{ sync.events_added }}</span>
                                        {% endif %}
                                        {% if sync.events_updated %}
                                        <span class="badge bg-warning">~{{ sync.events_updated }}</span>
                                        {% endif %}
                                        {% if sync.events_deleted %}
                                        <span class="badge bg-danger">-{{ sync.events_deleted }}</span>
                                        {% endif %}
                                    </div>
                                    {% else %}
                                    <small class="text-muted">No changes</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if sync.error_message %}
                                    <small class="text-danger" title="{{ sync.error_message }}">
                                        {{ sync.error_message[:50] }}{% if sync.error_message|length > 50 %}...{% endif %}
                                    </small>
                                    {% else %}
                                    <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No sync history available for this property.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Sync Modal -->
<div class="modal fade" id="syncModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Syncing Property {{ property.property_id }}</h5>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                    <div>
                        <div>Fetching latest calendar data...</div>
                        <small class="text-muted">This may take a few seconds</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function syncProperty() {
    const modal = new bootstrap.Modal(document.getElementById('syncModal'));
    modal.show();
    
    fetch(`/api/sync/property/{{ property.property_id }}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        modal.hide();
        
        if (data.status === 'success') {
            // Show success message and reload
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <strong>Sync Successful!</strong> 
                Processed ${data.events_processed} events. 
                Added: ${data.events_added}, Updated: ${data.events_updated}, Deleted: ${data.events_deleted}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('main').insertBefore(alert, document.querySelector('main').firstChild);
            
            // Reload page after a short delay
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            // Show error message
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                <strong>Sync Failed!</strong> ${data.error}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('main').insertBefore(alert, document.querySelector('main').firstChild);
        }
    })
    .catch(error => {
        modal.hide();
        alert('Error: ' + error);
    });
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
