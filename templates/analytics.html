{% extends "base.html" %}

{% block title %}Analytics - iCalendar Analytics{% endblock %}
{% block page_title %}Analytics & Reports{% endblock %}

{% block content %}
<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ filters.start_date or '' }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ filters.end_date or '' }}">
            </div>
            <div class="col-md-3">
                <label for="platform" class="form-label">Platform</label>
                <select class="form-select" id="platform" name="platform">
                    <option value="">All Platforms</option>
                    {% for platform in platforms %}
                    <option value="{{ platform.platform }}" {% if filters.platform == platform.platform %}selected{% endif %}>
                        {{ platform.platform|title }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        Apply Filters
                    </button>
                    <a href="{{ url_for('analytics_page') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Occupancy Overview -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Days</div>
                        <div class="h4 mb-0 text-white">{{ occupancy.overall.total_days|int }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Occupied Days</div>
                        <div class="h4 mb-0 text-white">{{ occupancy.overall.occupied_days|int }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bed fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Occupancy Rate</div>
                        <div class="h4 mb-0 text-white">{{ "%.1f"|format(occupancy.overall.occupancy_rate) }}%</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Revenue Opportunities</div>
                        <div class="h4 mb-0 text-white">{{ availability_gaps|length }}</div>
                        <div class="small text-white-50">Available gaps</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Occupancy by Platform
                </h5>
            </div>
            <div class="card-body">
                <div style="height: 250px; position: relative;">
                    <canvas id="platformOccupancyChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Booking Patterns
                </h5>
            </div>
            <div class="card-body">
                {% if booking_patterns and 'error' not in booking_patterns %}
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary">{{ booking_patterns.duration_stats.avg_duration }}</div>
                        <small class="text-muted">Avg Stay (days)</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success">{{ booking_patterns.total_bookings }}</div>
                        <small class="text-muted">Total Bookings</small>
                    </div>
                </div>

                {% if booking_patterns.seasonal_patterns.monthly %}
                <div style="height: 200px; position: relative;">
                    <canvas id="seasonalChart"></canvas>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                    <p>No booking data available</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Property Performance -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    Property Performance Ranking
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Property ID</th>
                                <th>Platform</th>
                                <th>Occupancy Rate</th>
                                <th>Total Days</th>
                                <th>Occupied Days</th>
                                <th>Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for property in occupancy.by_property[:10] %}
                            <tr>
                                <td>
                                    <span class="badge bg-{{ 'warning' if loop.index <= 3 else 'secondary' }}">
                                        #{{ loop.index }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('property_detail', property_id=property.property_id) }}" class="text-decoration-none fw-bold">
                                        {{ property.property_id }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ property.platform|title }}</span>
                                </td>
                                <td>
                                    <strong class="text-{{ 'success' if property.occupancy_rate > 80 else 'warning' if property.occupancy_rate > 50 else 'danger' }}">
                                        {{ "%.1f"|format(property.occupancy_rate) }}%
                                    </strong>
                                </td>
                                <td>{{ property.total_days }}</td>
                                <td>{{ property.occupied_days }}</td>
                                <td>
                                    <div class="progress progress-custom">
                                        <div class="progress-bar bg-{{ 'success' if property.occupancy_rate > 80 else 'warning' if property.occupancy_rate > 50 else 'danger' }}"
                                             style="width: {{ property.occupancy_rate }}%"></div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Opportunities -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-dollar-sign me-2"></i>
                    Revenue Opportunities (Availability Gaps)
                </h5>
            </div>
            <div class="card-body">
                {% if availability_gaps %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Property</th>
                                <th>Platform</th>
                                <th>Available Period</th>
                                <th>Gap Days</th>
                                <th>Opportunity</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for gap in availability_gaps[:10] %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('property_detail', property_id=gap.property_id) }}" class="text-decoration-none">
                                        {{ gap.property_id }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ gap.platform|title }}</span>
                                </td>
                                <td>
                                    <small>{{ gap.start_date }} to {{ gap.end_date }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if gap.gap_days > 100 else 'warning' if gap.gap_days > 30 else 'info' }}">
                                        {{ gap.gap_days }} days
                                    </span>
                                </td>
                                <td>
                                    <div class="progress progress-custom">
                                        <div class="progress-bar bg-success" style="width: {{ (gap.gap_days / 365 * 100)|round }}%"></div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                    <p>No significant availability gaps found</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Booking Conflicts
                </h5>
            </div>
            <div class="card-body">
                {% if conflicts %}
                {% for conflict in conflicts[:5] %}
                <div class="alert alert-warning">
                    <strong>Property {{ conflict.property_id }}</strong><br>
                    <small>
                        {{ conflict.platform1|title }}: {{ conflict.start1 }} - {{ conflict.end1 }}<br>
                        {{ conflict.platform2|title }}: {{ conflict.start2 }} - {{ conflict.end2 }}
                    </small>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                    <p>No booking conflicts detected</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-download me-2"></i>
                    Export Reports
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="exportReport('json')">
                        <i class="fas fa-file-code me-1"></i>
                        Export JSON
                    </button>
                    <button class="btn btn-outline-success" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv me-1"></i>
                        Export CSV
                    </button>
                    <button class="btn btn-outline-info" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>
                        Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Platform Occupancy Chart
const platformOccupancyData = {
    labels: [{% for platform in occupancy.by_platform %}'{{ platform.platform|title }}'{% if not loop.last %},{% endif %}{% endfor %}],
    datasets: [{
        label: 'Occupancy Rate (%)',
        data: [{% for platform in occupancy.by_platform %}{{ platform.occupancy_rate }}{% if not loop.last %},{% endif %}{% endfor %}],
        backgroundColor: 'rgba(54, 162, 235, 0.8)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
    }]
};

new Chart(document.getElementById('platformOccupancyChart'), {
    type: 'bar',
    data: platformOccupancyData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

{% if booking_patterns and booking_patterns.seasonal_patterns.monthly %}
// Seasonal Chart
const seasonalData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [{
        label: 'Bookings',
        data: [
            {% for month in range(1, 13) %}
            {{ booking_patterns.seasonal_patterns.monthly.get(month|string, 0) }}{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        backgroundColor: 'rgba(255, 99, 132, 0.8)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1
    }]
};

new Chart(document.getElementById('seasonalChart'), {
    type: 'line',
    data: seasonalData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    precision: 0
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        },
        elements: {
            point: {
                radius: 4,
                hoverRadius: 6
            },
            line: {
                tension: 0.3
            }
        }
    }
});
{% endif %}

function exportReport(format) {
    if (format === 'json') {
        fetch('/api/analytics/report')
            .then(response => response.json())
            .then(data => {
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'analytics_report.json';
                a.click();
                URL.revokeObjectURL(url);
            });
    } else if (format === 'csv') {
        // Convert occupancy data to CSV
        let csv = 'Property ID,Platform,Occupancy Rate,Total Days,Occupied Days\n';
        {% for property in occupancy.by_property %}
        csv += '{{ property.property_id }},{{ property.platform }},{{ property.occupancy_rate }},{{ property.total_days }},{{ property.occupied_days }}\n';
        {% endfor %}

        const blob = new Blob([csv], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'occupancy_report.csv';
        a.click();
        URL.revokeObjectURL(url);
    }
}
</script>
{% endblock %}
