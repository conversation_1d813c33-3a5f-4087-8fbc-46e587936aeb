{% extends "base.html" %}

{% block title %}Dashboard - iCalendar Analytics{% endblock %}
{% block page_title %}Dashboard Overview{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Properties</div>
                        <div class="h4 mb-0 text-white">{{ stats.total_properties }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-home fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Synced Properties</div>
                        <div class="h4 mb-0 text-white">{{ stats.synced_properties }}</div>
                        <div class="small text-white-50">
                            {{ "%.1f"|format((stats.synced_properties / stats.total_properties * 100) if stats.total_properties > 0 else 0) }}% complete
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-sync-alt fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Events</div>
                        <div class="h4 mb-0 text-white">{{ stats.total_events }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-check fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Sync Status</div>
                        <div class="h4 mb-0 text-white">
                            {% if sync_status.running %}
                                <i class="fas fa-spinner fa-spin"></i> Running
                            {% else %}
                                <i class="fas fa-check"></i> Ready
                            {% endif %}
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-server fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex gap-3 flex-wrap">
                    <a href="{{ url_for('properties') }}" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i>
                        View All Properties
                    </a>
                    <a href="{{ url_for('sync_page') }}" class="btn btn-success">
                        <i class="fas fa-sync-alt me-1"></i>
                        Sync Management
                    </a>
                    <button class="btn btn-info" onclick="startQuickSync()">
                        <i class="fas fa-play me-1"></i>
                        Start Full Sync
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="fas fa-refresh me-1"></i>
                        Refresh Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Platform Statistics -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>
                    Platform Distribution
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for platform in platform_stats %}
                    <div class="col-md-4 col-lg-3 mb-3">
                        <div class="text-center">
                            <div class="h3 mb-1 text-primary">{{ platform.count }}</div>
                            <div class="text-muted small">{{ platform.platform|title }}</div>
                            <div class="progress progress-custom mt-2">
                                <div class="progress-bar bg-success" style="width: {{ (platform.synced / platform.count * 100) if platform.count > 0 else 0 }}%"></div>
                            </div>
                            <small class="text-muted">{{ platform.synced }} synced</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Top Properties
                </h5>
            </div>
            <div class="card-body">
                {% for property in top_properties %}
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <div class="fw-bold">
                            <a href="{{ url_for('property_detail', property_id=property.property_id) }}" class="text-decoration-none">
                                Property {{ property.property_id }}
                            </a>
                        </div>
                        <small class="text-muted">{{ property.platform|title }}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-success">{{ property.event_count }}</div>
                        <small class="text-muted">events</small>
                    </div>
                </div>
                {% endfor %}
                
                {% if not top_properties %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-info-circle mb-2"></i>
                    <p class="mb-0">No synced properties yet</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Sync Activity
                </h5>
                <a href="{{ url_for('sync_page') }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                {% if recent_syncs %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Property</th>
                                <th>Status</th>
                                <th>Events</th>
                                <th>Time</th>
                                <th>Error</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sync in recent_syncs %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('property_detail', property_id=sync.property_id) }}" class="text-decoration-none">
                                        {{ sync.property_id }}
                                    </a>
                                </td>
                                <td>
                                    <span class="sync-status {{ sync.status }}"></span>
                                    <span class="badge bg-{{ 'success' if sync.status == 'success' else 'danger' }}">
                                        {{ sync.status|title }}
                                    </span>
                                </td>
                                <td>{{ sync.events_processed or 0 }}</td>
                                <td>
                                    <small class="text-muted">
                                        {{ sync.sync_start }}
                                    </small>
                                </td>
                                <td>
                                    {% if sync.error_message %}
                                    <small class="text-danger" title="{{ sync.error_message }}">
                                        {{ sync.error_message[:50] }}{% if sync.error_message|length > 50 %}...{% endif %}
                                    </small>
                                    {% else %}
                                    <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No sync activity yet. <a href="{{ url_for('sync_page') }}">Start your first sync</a></p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Sync Progress Modal -->
<div class="modal fade" id="quickSyncModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Starting Full Sync</h5>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                    <div>
                        <div>Initializing synchronization...</div>
                        <small class="text-muted">This will sync all properties</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function startQuickSync() {
    if (confirm('This will sync all properties. This may take several minutes. Continue?')) {
        const modal = new bootstrap.Modal(document.getElementById('quickSyncModal'));
        modal.show();
        
        fetch('/api/sync/start', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            modal.hide();
            if (data.error) {
                alert('Error: ' + data.error);
            } else {
                alert('Sync started! Check the Sync Management page for progress.');
                window.location.href = '{{ url_for("sync_page") }}';
            }
        })
        .catch(error => {
            modal.hide();
            alert('Error: ' + error);
        });
    }
}

// Auto-refresh sync status if running
{% if sync_status.running %}
setInterval(function() {
    fetch('/api/sync/status')
        .then(response => response.json())
        .then(data => {
            if (!data.running) {
                location.reload();
            }
        });
}, 5000);
{% endif %}
</script>
{% endblock %}
