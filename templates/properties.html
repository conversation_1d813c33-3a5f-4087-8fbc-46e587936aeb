{% extends "base.html" %}

{% block title %}Properties - iCalendar Analytics{% endblock %}
{% block page_title %}Properties Management{% endblock %}

{% block content %}
<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="platform" class="form-label">Platform</label>
                <select class="form-select" id="platform" name="platform">
                    <option value="">All Platforms</option>
                    {% for platform in platforms %}
                    <option value="{{ platform.platform }}" {% if platform_filter == platform.platform %}selected{% endif %}>
                        {{ platform.platform|title }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">Sync Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    {% for status in statuses %}
                    <option value="{{ status.sync_status }}" {% if status_filter == status.sync_status %}selected{% endif %}>
                        {{ status.sync_status|title }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        Filter
                    </button>
                    <a href="{{ url_for('properties') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Properties Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-home me-2"></i>
            Properties ({{ total }} total)
        </h5>
        <div class="btn-group">
            <button class="btn btn-sm btn-success" onclick="syncSelected()" id="syncSelectedBtn" disabled>
                <i class="fas fa-sync-alt me-1"></i>
                Sync Selected
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if properties %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>Property ID</th>
                        <th>Platform</th>
                        <th>Sync Status</th>
                        <th>Last Sync</th>
                        <th>Events</th>
                        <th>Occupancy</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for property in properties %}
                    <tr>
                        <td>
                            <input type="checkbox" class="property-checkbox" value="{{ property.property_id }}" onchange="updateSyncButton()">
                        </td>
                        <td>
                            <a href="{{ url_for('property_detail', property_id=property.property_id) }}" class="text-decoration-none fw-bold">
                                {{ property.property_id }}
                            </a>
                        </td>
                        <td>
                            <span class="badge badge-platform bg-{{ 'primary' if property.platform == 'airbnb' else 'secondary' }}">
                                {{ property.platform|title }}
                            </span>
                        </td>
                        <td>
                            <span class="sync-status {{ property.sync_status }}"></span>
                            <span class="badge bg-{{ 'success' if property.sync_status == 'success' else 'warning' if property.sync_status == 'pending' else 'danger' }}">
                                {{ property.sync_status|title }}
                            </span>
                        </td>
                        <td>
                            {% if property.last_sync %}
                            <small class="text-muted">{{ property.last_sync }}</small>
                            {% else %}
                            <small class="text-muted">Never</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex gap-2">
                                <span class="badge bg-info">{{ property.total_events }}</span>
                                {% if property.bookings > 0 %}
                                <span class="badge bg-success">{{ property.bookings }} bookings</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if property.true_occupancy_rate > 0 %}
                            <div class="d-flex align-items-center">
                                <div class="progress progress-custom me-2" style="width: 60px;">
                                    <div class="progress-bar" style="width: {{ property.true_occupancy_rate }}%"></div>
                                </div>
                                <small>{{ "%.1f"|format(property.true_occupancy_rate) }}%</small>
                            </div>
                            {% else %}
                            <small class="text-muted">No bookings</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="syncProperty('{{ property.property_id }}')" title="Sync Now">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <a href="{{ url_for('property_detail', property_id=property.property_id) }}" class="btn btn-outline-info" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if total_pages > 1 %}
        <nav aria-label="Properties pagination">
            <ul class="pagination justify-content-center">
                {% if current_page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('properties', page=current_page-1, platform=platform_filter, status=status_filter) }}">Previous</a>
                </li>
                {% endif %}

                {% for page_num in range(1, total_pages + 1) %}
                    {% if page_num == current_page %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('properties', page=page_num, platform=platform_filter, status=status_filter) }}">{{ page_num }}</a>
                    </li>
                    {% elif page_num == 4 or page_num == total_pages - 3 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if current_page < total_pages %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('properties', page=current_page+1, platform=platform_filter, status=status_filter) }}">Next</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-home fa-3x mb-3"></i>
            <h5>No properties found</h5>
            <p>Try adjusting your filters or check if properties have been loaded from CSV.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Sync Progress Modal -->
<div class="modal fade" id="syncModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Syncing Properties</h5>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                    <div>
                        <div id="syncModalText">Syncing properties...</div>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar" id="syncModalProgress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.property-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSyncButton();
}

function updateSyncButton() {
    const checkboxes = document.querySelectorAll('.property-checkbox:checked');
    const syncBtn = document.getElementById('syncSelectedBtn');

    syncBtn.disabled = checkboxes.length === 0;
    syncBtn.innerHTML = `<i class="fas fa-sync-alt me-1"></i>Sync Selected (${checkboxes.length})`;
}

function syncProperty(propertyId) {
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    fetch(`/api/sync/property/${propertyId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            location.reload();
        } else {
            alert('Sync failed: ' + data.error);
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        }
    })
    .catch(error => {
        alert('Error: ' + error);
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    });
}

function syncSelected() {
    const checkboxes = document.querySelectorAll('.property-checkbox:checked');
    const propertyIds = Array.from(checkboxes).map(cb => cb.value);

    if (propertyIds.length === 0) return;

    const modal = new bootstrap.Modal(document.getElementById('syncModal'));
    modal.show();

    let completed = 0;
    const total = propertyIds.length;

    function syncNext() {
        if (completed >= total) {
            modal.hide();
            location.reload();
            return;
        }

        const propertyId = propertyIds[completed];
        document.getElementById('syncModalText').textContent = `Syncing property ${propertyId}...`;

        fetch(`/api/sync/property/${propertyId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            completed++;
            const progress = (completed / total) * 100;
            document.getElementById('syncModalProgress').style.width = progress + '%';

            setTimeout(syncNext, 500); // Small delay between requests
        })
        .catch(error => {
            console.error('Sync error:', error);
            completed++;
            setTimeout(syncNext, 500);
        });
    }

    syncNext();
}
</script>
{% endblock %}
