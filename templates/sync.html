{% extends "base.html" %}

{% block title %}Sync Management - iCalendar Analytics{% endblock %}
{% block page_title %}Sync Management{% endblock %}

{% block content %}
<!-- Sync Controls -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sync-alt me-2"></i>
                    Sync Controls
                </h5>
            </div>
            <div class="card-body">
                {% if sync_status.running %}
                <div class="alert alert-info">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                        <div class="flex-grow-1">
                            <strong>Sync in Progress</strong><br>
                            <small>Property {{ sync_status.current_property }} ({{ sync_status.progress }}/{{ sync_status.total }})</small>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar" style="width: {{ (sync_status.progress / sync_status.total * 100) if sync_status.total > 0 else 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="d-flex gap-3">
                    <button class="btn btn-primary" onclick="startFullSync()" id="fullSyncBtn">
                        <i class="fas fa-sync-alt me-1"></i>
                        Sync All Properties
                    </button>
                    <button class="btn btn-outline-primary" onclick="syncPending()" id="pendingSyncBtn">
                        <i class="fas fa-clock me-1"></i>
                        Sync Pending Only
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshStatus()">
                        <i class="fas fa-refresh me-1"></i>
                        Refresh Status
                    </button>
                </div>
                {% endif %}
                
                {% if sync_status.results %}
                <div class="mt-3">
                    <div class="alert alert-{{ 'success' if sync_status.results.get('error') is none else 'danger' }}">
                        {% if sync_status.results.get('error') %}
                        <strong>Sync Failed:</strong> {{ sync_status.results.error }}
                        {% else %}
                        <strong>Sync Completed!</strong><br>
                        Successful: {{ sync_status.results.successful }}, Failed: {{ sync_status.results.failed }}
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Sync Statistics
                </h5>
            </div>
            <div class="card-body">
                {% for stat in sync_stats %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <span class="sync-status {{ stat.sync_status }}"></span>
                        {{ stat.sync_status|title }}
                    </div>
                    <span class="badge bg-{{ 'success' if stat.sync_status == 'success' else 'warning' if stat.sync_status == 'pending' else 'danger' }}">
                        {{ stat.count }}
                    </span>
                </div>
                {% endfor %}
                
                {% if sync_stats %}
                <hr>
                <small class="text-muted">
                    Last sync: {{ sync_stats[0].latest_sync or 'Never' }}
                </small>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Pending Properties -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Properties Needing Sync
                </h5>
                <span class="badge bg-warning">{{ pending_properties|length }} pending</span>
            </div>
            <div class="card-body">
                {% if pending_properties %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAllPending" onchange="toggleSelectAllPending()">
                                </th>
                                <th>Property ID</th>
                                <th>Platform</th>
                                <th>Status</th>
                                <th>Last Sync</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for property in pending_properties %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="pending-checkbox" value="{{ property.property_id }}">
                                </td>
                                <td>
                                    <a href="{{ url_for('property_detail', property_id=property.property_id) }}" class="text-decoration-none">
                                        {{ property.property_id }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ property.platform|title }}</span>
                                </td>
                                <td>
                                    <span class="sync-status {{ property.sync_status }}"></span>
                                    <span class="badge bg-{{ 'warning' if property.sync_status == 'pending' else 'danger' }}">
                                        {{ property.sync_status|title }}
                                    </span>
                                </td>
                                <td>
                                    {% if property.last_sync %}
                                    <small class="text-muted">{{ property.last_sync }}</small>
                                    {% else %}
                                    <small class="text-muted">Never</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="syncSingleProperty('{{ property.property_id }}')">
                                        <i class="fas fa-sync-alt"></i>
                                        Sync Now
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="syncSelectedPending()" id="syncSelectedPendingBtn" disabled>
                        <i class="fas fa-sync-alt me-1"></i>
                        Sync Selected (0)
                    </button>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                    <h5>All Properties Synced!</h5>
                    <p>No properties need synchronization at this time.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Sync History -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Sync History
                </h5>
            </div>
            <div class="card-body">
                {% if recent_syncs %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Property</th>
                                <th>Start Time</th>
                                <th>End Time</th>
                                <th>Status</th>
                                <th>Events</th>
                                <th>Changes</th>
                                <th>Error</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sync in recent_syncs %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('property_detail', property_id=sync.property_id) }}" class="text-decoration-none">
                                        {{ sync.property_id }}
                                    </a>
                                </td>
                                <td>
                                    <small class="text-muted">{{ sync.sync_start }}</small>
                                </td>
                                <td>
                                    {% if sync.sync_end %}
                                    <small class="text-muted">{{ sync.sync_end }}</small>
                                    {% else %}
                                    <small class="text-warning">Running...</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="sync-status {{ sync.status }}"></span>
                                    <span class="badge bg-{{ 'success' if sync.status == 'success' else 'danger' }}">
                                        {{ sync.status|title }}
                                    </span>
                                </td>
                                <td>{{ sync.events_processed or 0 }}</td>
                                <td>
                                    {% if sync.events_added or sync.events_updated or sync.events_deleted %}
                                    <small class="text-muted">
                                        {% if sync.events_added %}<span class="text-success">+{{ sync.events_added }}</span>{% endif %}
                                        {% if sync.events_updated %}<span class="text-warning">~{{ sync.events_updated }}</span>{% endif %}
                                        {% if sync.events_deleted %}<span class="text-danger">-{{ sync.events_deleted }}</span>{% endif %}
                                    </small>
                                    {% else %}
                                    <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if sync.error_message %}
                                    <small class="text-danger" title="{{ sync.error_message }}">
                                        {{ sync.error_message[:30] }}{% if sync.error_message|length > 30 %}...{% endif %}
                                    </small>
                                    {% else %}
                                    <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No sync history available.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Sync Progress Modal -->
<div class="modal fade" id="syncProgressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Synchronization Progress</h5>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                    <div class="flex-grow-1">
                        <div id="syncProgressText">Starting synchronization...</div>
                        <div class="progress mt-2" style="height: 8px;">
                            <div class="progress-bar" id="syncProgressBar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="syncProgressDetails">Preparing...</small>
                    </div>
                </div>
                <div id="syncResults" class="d-none">
                    <div class="alert alert-success">
                        <strong>Sync Completed!</strong>
                        <div id="syncResultsText"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="syncCloseBtn" disabled>Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let syncModal;

function startFullSync() {
    if (confirm('This will sync all properties. This may take several minutes. Continue?')) {
        showSyncModal();
        
        fetch('/api/sync/start', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error: ' + data.error);
                hideSyncModal();
            } else {
                pollSyncStatus();
            }
        })
        .catch(error => {
            alert('Error: ' + error);
            hideSyncModal();
        });
    }
}

function showSyncModal() {
    syncModal = new bootstrap.Modal(document.getElementById('syncProgressModal'));
    syncModal.show();
    document.getElementById('syncCloseBtn').disabled = true;
}

function hideSyncModal() {
    if (syncModal) {
        syncModal.hide();
    }
}

function pollSyncStatus() {
    const interval = setInterval(() => {
        fetch('/api/sync/status')
            .then(response => response.json())
            .then(data => {
                updateSyncProgress(data);
                
                if (!data.running) {
                    clearInterval(interval);
                    showSyncResults(data);
                    document.getElementById('syncCloseBtn').disabled = false;
                    
                    // Reload page after a delay
                    setTimeout(() => {
                        location.reload();
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Error polling sync status:', error);
                clearInterval(interval);
            });
    }, 2000);
}

function updateSyncProgress(data) {
    const progress = data.total > 0 ? (data.progress / data.total) * 100 : 0;
    
    document.getElementById('syncProgressText').textContent = 
        `Syncing properties... (${data.progress}/${data.total})`;
    document.getElementById('syncProgressBar').style.width = progress + '%';
    document.getElementById('syncProgressDetails').textContent = 
        data.current_property ? `Current: Property ${data.current_property}` : 'Processing...';
}

function showSyncResults(data) {
    if (data.results) {
        const results = data.results;
        document.getElementById('syncResultsText').innerHTML = 
            `Successful: ${results.successful}, Failed: ${results.failed}`;
        document.getElementById('syncResults').classList.remove('d-none');
    }
}

function toggleSelectAllPending() {
    const selectAll = document.getElementById('selectAllPending');
    const checkboxes = document.querySelectorAll('.pending-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSyncSelectedButton();
}

function updateSyncSelectedButton() {
    const checkboxes = document.querySelectorAll('.pending-checkbox:checked');
    const btn = document.getElementById('syncSelectedPendingBtn');
    
    btn.disabled = checkboxes.length === 0;
    btn.innerHTML = `<i class="fas fa-sync-alt me-1"></i>Sync Selected (${checkboxes.length})`;
}

function syncSelectedPending() {
    const checkboxes = document.querySelectorAll('.pending-checkbox:checked');
    const propertyIds = Array.from(checkboxes).map(cb => cb.value);
    
    if (propertyIds.length === 0) return;
    
    showSyncModal();
    
    let completed = 0;
    const total = propertyIds.length;
    
    function syncNext() {
        if (completed >= total) {
            document.getElementById('syncProgressText').textContent = 'Sync completed!';
            document.getElementById('syncCloseBtn').disabled = false;
            setTimeout(() => location.reload(), 2000);
            return;
        }
        
        const propertyId = propertyIds[completed];
        document.getElementById('syncProgressText').textContent = 
            `Syncing property ${propertyId}... (${completed + 1}/${total})`;
        document.getElementById('syncProgressBar').style.width = 
            ((completed / total) * 100) + '%';
        
        fetch(`/api/sync/property/${propertyId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            completed++;
            setTimeout(syncNext, 500);
        })
        .catch(error => {
            console.error('Sync error:', error);
            completed++;
            setTimeout(syncNext, 500);
        });
    }
    
    syncNext();
}

function syncSingleProperty(propertyId) {
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    fetch(`/api/sync/property/${propertyId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            location.reload();
        } else {
            alert('Sync failed: ' + data.error);
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        }
    })
    .catch(error => {
        alert('Error: ' + error);
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    });
}

function refreshStatus() {
    location.reload();
}

// Add event listeners for pending checkboxes
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.pending-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSyncSelectedButton);
    });
});

// Auto-refresh if sync is running
{% if sync_status.running %}
setTimeout(() => {
    location.reload();
}, 10000);
{% endif %}
</script>
{% endblock %}
