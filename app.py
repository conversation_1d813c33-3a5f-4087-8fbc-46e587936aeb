#!/usr/bin/env python3
"""
iCalendar Analytics Web Application
A comprehensive web interface for property calendar data management and analytics.
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for, flash
import sqlite3
import json
from datetime import datetime, timedelta
from ical_database_sync import ICalDatabaseSync
from ical_analytics import ICalAnalytics
import threading
import time

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# Global instances
sync_system = ICalDatabaseSync("ical_data.db")
analytics = ICalAnalytics("ical_data.db")

# Global sync status
sync_status = {
    'running': False,
    'progress': 0,
    'total': 0,
    'current_property': '',
    'results': None
}

def get_db_connection():
    """Get database connection."""
    conn = sqlite3.connect('ical_data.db')
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def dashboard():
    """Main dashboard page."""
    conn = get_db_connection()

    # Get overall statistics
    stats = analytics.get_property_stats()

    # Get recent sync history
    recent_syncs = conn.execute("""
        SELECT property_id, sync_start, status, events_processed, error_message
        FROM sync_history
        ORDER BY sync_start DESC
        LIMIT 10
    """).fetchall()

    # Get platform distribution
    platform_stats = conn.execute("""
        SELECT platform, COUNT(*) as count,
               SUM(CASE WHEN sync_status = 'success' THEN 1 ELSE 0 END) as synced
        FROM properties
        GROUP BY platform
    """).fetchall()

    # Get top performing properties by TRUE occupancy rate
    top_properties = conn.execute("""
        SELECT property_id, platform, true_occupancy_rate, total_days,
               available_days, booked_days, blocked_days, calendar_blocked_rate
        FROM occupancy_stats
        WHERE total_days > 30
        ORDER BY true_occupancy_rate DESC
        LIMIT 5
    """).fetchall()

    conn.close()

    return render_template('dashboard.html',
                         stats=stats,
                         recent_syncs=recent_syncs,
                         platform_stats=platform_stats,
                         top_properties=top_properties,
                         sync_status=sync_status)

@app.route('/properties')
def properties():
    """Properties listing page."""
    page = request.args.get('page', 1, type=int)
    platform_filter = request.args.get('platform', '')
    status_filter = request.args.get('status', '')

    conn = get_db_connection()

    # Build query with filters
    where_conditions = []
    params = []

    if platform_filter:
        where_conditions.append("p.platform = ?")
        params.append(platform_filter)

    if status_filter:
        where_conditions.append("p.sync_status = ?")
        params.append(status_filter)

    where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

    # Get total count
    count_query = f"SELECT COUNT(*) FROM properties p {where_clause}"
    total = conn.execute(count_query, params).fetchone()[0]

    # Get properties with pagination
    per_page = 20
    offset = (page - 1) * per_page

    properties_query = f"""
        SELECT p.*,
               COALESCE(ps.total_events, 0) as total_events,
               COALESCE(ps.bookings, 0) as bookings,
               COALESCE(os.true_occupancy_rate, 0) as true_occupancy_rate,
               COALESCE(os.booked_days, 0) as booked_days,
               COALESCE(os.available_days, 0) as available_days,
               COALESCE(os.blocked_days, 0) as blocked_days,
               COALESCE(os.calendar_blocked_rate, 0) as calendar_blocked_rate
        FROM properties p
        LEFT JOIN property_summary ps ON p.property_id = ps.property_id
        LEFT JOIN occupancy_stats os ON p.property_id = os.property_id
        {where_clause}
        ORDER BY p.last_sync DESC NULLS LAST
        LIMIT ? OFFSET ?
    """

    properties_list = conn.execute(properties_query, params + [per_page, offset]).fetchall()

    # Get available platforms and statuses for filters
    platforms = conn.execute("SELECT DISTINCT platform FROM properties ORDER BY platform").fetchall()
    statuses = conn.execute("SELECT DISTINCT sync_status FROM properties ORDER BY sync_status").fetchall()

    conn.close()

    # Calculate pagination
    total_pages = (total + per_page - 1) // per_page

    return render_template('properties.html',
                         properties=properties_list,
                         platforms=platforms,
                         statuses=statuses,
                         current_page=page,
                         total_pages=total_pages,
                         platform_filter=platform_filter,
                         status_filter=status_filter,
                         total=total)

@app.route('/property/<property_id>')
def property_detail(property_id):
    """Property detail page."""
    conn = get_db_connection()

    # Get property info
    property_info = conn.execute("""
        SELECT * FROM properties WHERE property_id = ?
    """, (property_id,)).fetchone()

    if not property_info:
        flash('Property not found', 'error')
        return redirect(url_for('properties'))

    # Get property stats
    property_stats = analytics.get_property_stats(property_id)

    # Get recent events
    events = conn.execute("""
        SELECT * FROM calendar_events
        WHERE property_id = ?
        ORDER BY start_date DESC
        LIMIT 20
    """, (property_id,)).fetchall()

    # Get sync history
    sync_history = conn.execute("""
        SELECT * FROM sync_history
        WHERE property_id = ?
        ORDER BY sync_start DESC
        LIMIT 10
    """, (property_id,)).fetchall()

    conn.close()

    return render_template('property_detail.html',
                         property=property_info,
                         stats=property_stats,
                         events=events,
                         sync_history=sync_history)

@app.route('/analytics')
def analytics_page():
    """Analytics and reports page."""
    # Get date range from query params
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    platform_filter = request.args.get('platform')

    # Get occupancy analysis
    occupancy = analytics.get_occupancy_analysis(start_date, end_date, platform_filter)

    # Get booking patterns with filters
    booking_patterns = analytics.get_booking_patterns(
        start_date=start_date,
        end_date=end_date,
        platform=platform_filter
    )

    # Get availability gaps with filters
    availability_gaps = analytics.find_availability_gaps(
        start_date=start_date,
        end_date=end_date,
        platform=platform_filter
    )

    # Get conflicts with filters
    conflicts = analytics.detect_conflicts(
        start_date=start_date,
        end_date=end_date,
        platform=platform_filter
    )

    # Get available platforms for filter
    conn = get_db_connection()
    platforms = conn.execute("SELECT DISTINCT platform FROM properties ORDER BY platform").fetchall()
    conn.close()

    return render_template('analytics.html',
                         occupancy=occupancy,
                         booking_patterns=booking_patterns,
                         availability_gaps=availability_gaps,
                         conflicts=conflicts,
                         platforms=platforms,
                         filters={
                             'start_date': start_date,
                             'end_date': end_date,
                             'platform': platform_filter
                         })

@app.route('/sync')
def sync_page():
    """Sync management page."""
    conn = get_db_connection()

    # Get sync statistics
    sync_stats = conn.execute("""
        SELECT
            sync_status,
            COUNT(*) as count,
            MAX(last_sync) as latest_sync
        FROM properties
        GROUP BY sync_status
    """).fetchall()

    # Get recent sync history
    recent_syncs = conn.execute("""
        SELECT * FROM sync_history
        ORDER BY sync_start DESC
        LIMIT 20
    """).fetchall()

    # Get properties that need sync (never synced or failed)
    pending_properties = conn.execute("""
        SELECT property_id, platform, sync_status, last_sync
        FROM properties
        WHERE sync_status IN ('pending', 'failed') OR last_sync IS NULL
        ORDER BY platform, property_id
        LIMIT 50
    """).fetchall()

    conn.close()

    return render_template('sync.html',
                         sync_stats=sync_stats,
                         recent_syncs=recent_syncs,
                         pending_properties=pending_properties,
                         sync_status=sync_status)

def background_sync_all():
    """Background function to sync all properties."""
    global sync_status

    try:
        sync_status['running'] = True
        sync_status['progress'] = 0

        # Get all properties
        conn = get_db_connection()
        properties = conn.execute("SELECT property_id FROM properties").fetchall()
        conn.close()

        sync_status['total'] = len(properties)

        results = {
            'successful': 0,
            'failed': 0,
            'details': []
        }

        for i, prop in enumerate(properties):
            property_id = prop['property_id']
            sync_status['current_property'] = property_id
            sync_status['progress'] = i + 1

            result = sync_system.sync_property(property_id)

            if result['status'] == 'success':
                results['successful'] += 1
            else:
                results['failed'] += 1

            results['details'].append({
                'property_id': property_id,
                'result': result
            })

            # Small delay to avoid overwhelming servers
            time.sleep(0.2)

        sync_status['results'] = results

    except Exception as e:
        sync_status['results'] = {'error': str(e)}
    finally:
        sync_status['running'] = False
        sync_status['current_property'] = ''

@app.route('/api/sync/start', methods=['POST'])
def start_sync():
    """Start background sync process."""
    if sync_status['running']:
        return jsonify({'error': 'Sync already running'}), 400

    # Start background sync
    thread = threading.Thread(target=background_sync_all)
    thread.daemon = True
    thread.start()

    return jsonify({'message': 'Sync started'})

@app.route('/api/sync/status')
def sync_status_api():
    """Get current sync status."""
    return jsonify(sync_status)

@app.route('/api/sync/property/<property_id>', methods=['POST'])
def sync_single_property(property_id):
    """Sync a single property."""
    if sync_status['running']:
        return jsonify({'error': 'Bulk sync is running'}), 400

    result = sync_system.sync_property(property_id)
    return jsonify(result)

@app.route('/api/analytics/report')
def analytics_report_api():
    """Generate and return analytics report."""
    report = analytics.generate_report()
    return jsonify(report)

if __name__ == '__main__':
    print("🚀 Starting iCalendar Analytics Web Application...")
    print("📊 Dashboard will be available at: http://localhost:8888")
    print("=" * 60)
    app.run(debug=True, host='0.0.0.0', port=8888)
